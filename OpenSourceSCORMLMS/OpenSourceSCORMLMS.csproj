<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp2.1</TargetFramework>
    <UserSecretsId>aspnet-OpenSourceSCORMLMS-216CDD04-809C-4E90-8838-B15D3F9DACB5</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="appsettings.Production..json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Bower" Version="1.3.11" />
    <PackageReference Include="Karambolo.Extensions.Logging.File" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="2.1.14" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="2.1.0" PrivateAssets="All" />
    <PackageReference Include="Newtonsoft.Json" Version="11.0.2" />
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Areas\Identity\Services\" />
    <Folder Include="Data\Migrations\" />
    <Folder Include="SCORMPackages\" />
  </ItemGroup>

  <!-- Temporarily commented out SCORM XML dependencies for macOS compatibility
  <ItemGroup>
    <ProjectReference Include="..\SCORM_XMLObjects\V1_2\adlcp_rootv1p2\adlcp_rootv1p2.csproj" />
    <ProjectReference Include="..\SCORM_XMLObjects\V1_2\Altova\Altova.csproj" />
    <ProjectReference Include="..\SCORM_XMLObjects\V1_2\imscp_rootv1p1p2\imscp_rootv1p1p2.csproj" />
    <ProjectReference Include="..\SCORM_XMLObjects\V1_2\imsmd_rootv1p2p1\imsmd_rootv1p2p1.csproj" />
    <ProjectReference Include="..\SCORM_XMLObjects\V1_3\adlcp_v1p3\adlcp_v1P3.csproj" />
  </ItemGroup>
  -->
</Project>
