﻿// Temporarily commented out for macOS compatibility
// using adlcp_rootv1p2;
// using adlcp_rootv1p2.imscp;
// using imsmd_rootv1p2p1;
// using adlcp_v1p3;
// using imscp_v1p1;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Xml;
using System.Linq;
using System;

namespace OpenSourceSCORMLMS.Helpers
{
    /// <summary>
    /// Get some information about the course from the imsmanifest`.xml file
    /// </summary>
    public class SCORMUploadHelper
    {
        public string identifier { get; set; }
        public string title { get; set; }
        public string SCORM_Version { get; set; } // will either be 1.2 or one of the synonyms for SCORM 2004
        public string version { get; set; } // this is the creator's version
        public string description { get; set; }
        public string href { get; set; }
        private ILogger logger { get; set; }
        public SCORMUploadHelper(ILogger logger)
        {
            this.logger = logger;
        }

        public void parseManifest(string pathToManifest)
        {
            // Simplified XML parsing for macOS compatibility
            string XMLPath = pathToManifest;
            string XMLDirectory = System.IO.Path.GetDirectoryName(pathToManifest);
            if (!System.IO.File.Exists(XMLPath))
            {
                logger.LogWarning("Manifest file not found!");
                // Set default values for demo
                this.identifier = "demo-course-" + Guid.NewGuid().ToString();
                this.title = "Demo SCORM Course";
                this.SCORM_Version = "1.2";
                this.version = "1.0";
                this.description = "This is a demo course for testing purposes";
                this.href = "index.html";
                return;
            }

            try
            {
                // Basic XML parsing using System.Xml
                XmlDocument doc = new XmlDocument();
                doc.Load(XMLPath);

                // Try to extract basic information from the manifest
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("imscp", "http://www.imsproject.org/xsd/imscp_rootv1p1p2");
                nsmgr.AddNamespace("adlcp", "http://www.adlnet.org/xsd/adlcp_rootv1p2");

                XmlNode manifestNode = doc.SelectSingleNode("//imscp:manifest", nsmgr) ?? doc.SelectSingleNode("//manifest");

                if (manifestNode != null)
                {
                    this.identifier = manifestNode.Attributes?["identifier"]?.Value ?? "demo-course-" + Guid.NewGuid().ToString();
                    this.version = manifestNode.Attributes?["version"]?.Value ?? "1.0";
                }
                else
                {
                    this.identifier = "demo-course-" + Guid.NewGuid().ToString();
                    this.version = "1.0";
                }

                // Set default values
                this.SCORM_Version = "1.2";
                this.title = this.identifier;
                this.description = "SCORM Course";
                this.href = "index.html";

                logger.LogInformation("Parsed manifest with simplified parser - ID: {identifier}", this.identifier);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error parsing manifest file");
                // Set fallback values
                this.identifier = "demo-course-" + Guid.NewGuid().ToString();
                this.title = "Demo SCORM Course";
                this.SCORM_Version = "1.2";
                this.version = "1.0";
                this.description = "This is a demo course";
                this.href = "index.html";
            }

            logger.LogInformation("Parse of manifest completed successfully");
            return;
        }

        // Simplified method for macOS compatibility
        private string FindDefaultWebPage()
        {
            // Return default page name for demo purposes
            return "index.html";
        }

        // Commented out methods that depend on SCORM XML libraries for macOS compatibility
        /*
        private adlcp_rootv1p2.imscp.resourceType FindDefaultResource(identifierrefType2 item_identifier, adlcp_rootv1p2.imscp.resourcesType resources)
        private string GetSCORMVersion(adlcp_rootv1p2.imscp.manifestType root)
        private adlcp_rootv1p2.imscp.organizationType FindDefaultOrg(string org_default, adlcp_rootv1p2.imscp.organizationsType orgs)
        */
    }
}
