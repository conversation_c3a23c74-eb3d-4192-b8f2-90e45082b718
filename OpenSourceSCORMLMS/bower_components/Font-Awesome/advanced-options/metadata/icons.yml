500px:
  changes:
    - '4.4'
    - 5.0.0
  label: 500px
  search:
    terms: []
  styles:
    - brands
  unicode: f26e
accessible-icon:
  changes:
    - 5.0.0
  label: Accessible Icon
  search:
    terms:
      - accessibility
      - wheelchair
      - handicap
      - person
      - wheelchair-alt
  styles:
    - brands
  unicode: f368
accusoft:
  changes:
    - 5.0.0
  label: Accusoft
  search:
    terms: []
  styles:
    - brands
  unicode: f369
address-book:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
  label: Address Book
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2b9
address-card:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
  label: Address Card
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2bb
adjust:
  changes:
    - '1'
    - 5.0.0
  label: adjust
  search:
    terms:
      - contrast
  styles:
    - solid
  unicode: f042
adn:
  changes:
    - '3.2'
    - 5.0.0
  label: App.net
  search:
    terms: []
  styles:
    - brands
  unicode: f170
adversal:
  changes:
    - 5.0.0
  label: Adversal
  search:
    terms: []
  sponsored:
    label: Adversal
    url: 'https://www.adversal.com'
  styles:
    - brands
  unicode: f36a
affiliatetheme:
  changes:
    - 5.0.0
  label: affiliatetheme
  search:
    terms: []
  sponsored:
    label: affiliatetheme
    url: 'https://affiliatetheme.io/en'
  styles:
    - brands
  unicode: f36b
algolia:
  changes:
    - 5.0.0
  label: Algolia
  search:
    terms: []
  sponsored:
    label: Algolia
    url: 'http://www.algolia.com'
  styles:
    - brands
  unicode: f36c
align-center:
  changes:
    - '1'
    - 5.0.0
  label: align-center
  search:
    terms:
      - middle
      - text
  styles:
    - solid
  unicode: f037
align-justify:
  changes:
    - '1'
    - 5.0.0
  label: align-justify
  search:
    terms:
      - text
  styles:
    - solid
  unicode: f039
align-left:
  changes:
    - '1'
    - 5.0.0
  label: align-left
  search:
    terms:
      - text
  styles:
    - solid
  unicode: f036
align-right:
  changes:
    - '1'
    - 5.0.0
  label: align-right
  search:
    terms:
      - text
  styles:
    - solid
  unicode: f038
allergies:
  changes:
    - 5.0.7
  label: Allergies
  search:
    terms:
      - intolerances
      - pox
      - hand
      - freckles
      - spots
  styles:
    - solid
  unicode: f461
amazon:
  changes:
    - '4.4'
    - 5.0.0
  label: Amazon
  search:
    terms: []
  styles:
    - brands
  unicode: f270
amazon-pay:
  changes:
    - 5.0.2
  label: Amazon Pay
  search:
    terms: []
  styles:
    - brands
  unicode: f42c
ambulance:
  changes:
    - '3'
    - 5.0.0
    - 5.0.7
  label: ambulance
  search:
    terms:
      - vehicle
      - support
      - help
      - machine
  styles:
    - solid
  unicode: f0f9
american-sign-language-interpreting:
  changes:
    - '4.6'
    - 5.0.0
  label: American Sign Language Interpreting
  search:
    terms: []
  styles:
    - solid
  unicode: f2a3
amilia:
  changes:
    - 5.0.0
  label: Amilia
  search:
    terms: []
  sponsored:
    label: Amilia
    url: 'http://www.amilia.com'
  styles:
    - brands
  unicode: f36d
anchor:
  changes:
    - '3.1'
    - 5.0.0
  label: Anchor
  search:
    terms:
      - link
  styles:
    - solid
  unicode: f13d
android:
  changes:
    - '3.2'
    - 5.0.0
  label: Android
  search:
    terms:
      - robot
  styles:
    - brands
  unicode: f17b
angellist:
  changes:
    - '4.2'
    - 5.0.0
  label: AngelList
  search:
    terms: []
  styles:
    - brands
  unicode: f209
angle-double-down:
  changes:
    - '3'
    - 5.0.0
  label: Angle Double Down
  search:
    terms:
      - arrows
  styles:
    - solid
  unicode: f103
angle-double-left:
  changes:
    - '3'
    - 5.0.0
  label: Angle Double Left
  search:
    terms:
      - laquo
      - quote
      - previous
      - back
      - arrows
  styles:
    - solid
  unicode: f100
angle-double-right:
  changes:
    - '3'
    - 5.0.0
  label: Angle Double Right
  search:
    terms:
      - raquo
      - quote
      - next
      - forward
      - arrows
  styles:
    - solid
  unicode: f101
angle-double-up:
  changes:
    - '3'
    - 5.0.0
  label: Angle Double Up
  search:
    terms:
      - arrows
  styles:
    - solid
  unicode: f102
angle-down:
  changes:
    - '3'
    - 5.0.0
  label: angle-down
  search:
    terms:
      - arrow
  styles:
    - solid
  unicode: f107
angle-left:
  changes:
    - '3'
    - 5.0.0
  label: angle-left
  search:
    terms:
      - previous
      - back
      - arrow
  styles:
    - solid
  unicode: f104
angle-right:
  changes:
    - '3'
    - 5.0.0
  label: angle-right
  search:
    terms:
      - next
      - forward
      - arrow
  styles:
    - solid
  unicode: f105
angle-up:
  changes:
    - '3'
    - 5.0.0
  label: angle-up
  search:
    terms:
      - arrow
  styles:
    - solid
  unicode: f106
angrycreative:
  changes:
    - 5.0.0
  label: Angry Creative
  search:
    terms: []
  sponsored:
    label: Angry Creative
    url: 'https://angrycreative.se'
  styles:
    - brands
  unicode: f36e
angular:
  changes:
    - 5.0.0
  label: Angular
  search:
    terms: []
  styles:
    - brands
  unicode: f420
app-store:
  changes:
    - 5.0.0
  label: App Store
  search:
    terms: []
  styles:
    - brands
  unicode: f36f
app-store-ios:
  changes:
    - 5.0.0
  label: iOS App Store
  search:
    terms: []
  styles:
    - brands
  unicode: f370
apper:
  changes:
    - 5.0.0
  label: Apper Systems AB
  search:
    terms: []
  sponsored:
    label: Apper Systems AB
    url: 'http://www.apper.com'
  styles:
    - brands
  unicode: f371
apple:
  changes:
    - '3.2'
    - 5.0.0
    - 5.0.7
  label: Apple
  search:
    terms:
      - osx
      - food
  styles:
    - brands
  unicode: f179
apple-pay:
  changes:
    - 5.0.0
  label: Apple Pay
  search:
    terms: []
  styles:
    - brands
  unicode: f415
archive:
  changes:
    - '3.2'
    - 5.0.0
    - 5.0.9
  label: Archive
  search:
    terms:
      - box
      - storage
      - package
  styles:
    - solid
  unicode: f187
arrow-alt-circle-down:
  changes:
    - 5.0.0
  label: Alternate Arrow Circle Down
  search:
    terms:
      - download
      - arrow-circle-o-down
  styles:
    - solid
    - regular
  unicode: f358
arrow-alt-circle-left:
  changes:
    - 5.0.0
  label: Alternate Arrow Circle Left
  search:
    terms:
      - previous
      - back
      - arrow-circle-o-left
  styles:
    - solid
    - regular
  unicode: f359
arrow-alt-circle-right:
  changes:
    - 5.0.0
  label: Alternate Arrow Circle Right
  search:
    terms:
      - next
      - forward
      - arrow-circle-o-right
  styles:
    - solid
    - regular
  unicode: f35a
arrow-alt-circle-up:
  changes:
    - 5.0.0
  label: Alternate Arrow Circle Up
  search:
    terms:
      - arrow-circle-o-up
  styles:
    - solid
    - regular
  unicode: f35b
arrow-circle-down:
  changes:
    - '2'
    - 5.0.0
  label: Arrow Circle Down
  search:
    terms:
      - download
  styles:
    - solid
  unicode: f0ab
arrow-circle-left:
  changes:
    - '2'
    - 5.0.0
  label: Arrow Circle Left
  search:
    terms:
      - previous
      - back
  styles:
    - solid
  unicode: f0a8
arrow-circle-right:
  changes:
    - '2'
    - 5.0.0
  label: Arrow Circle Right
  search:
    terms:
      - next
      - forward
  styles:
    - solid
  unicode: f0a9
arrow-circle-up:
  changes:
    - '2'
    - 5.0.0
  label: Arrow Circle Up
  search:
    terms: []
  styles:
    - solid
  unicode: f0aa
arrow-down:
  changes:
    - '1'
    - 5.0.0
  label: arrow-down
  search:
    terms:
      - download
  styles:
    - solid
  unicode: f063
arrow-left:
  changes:
    - '1'
    - 5.0.0
  label: arrow-left
  search:
    terms:
      - previous
      - back
  styles:
    - solid
  unicode: f060
arrow-right:
  changes:
    - '1'
    - 5.0.0
  label: arrow-right
  search:
    terms:
      - next
      - forward
  styles:
    - solid
  unicode: f061
arrow-up:
  changes:
    - '1'
    - 5.0.0
  label: arrow-up
  search:
    terms: []
  styles:
    - solid
  unicode: f062
arrows-alt:
  changes:
    - '2'
    - 5.0.0
  label: Alternate Arrows
  search:
    terms:
      - expand
      - enlarge
      - fullscreen
      - bigger
      - move
      - reorder
      - resize
      - arrow
      - arrows
  styles:
    - solid
  unicode: f0b2
arrows-alt-h:
  changes:
    - 5.0.0
  label: Alternate Arrows Horizontal
  search:
    terms:
      - resize
      - arrows-h
  styles:
    - solid
  unicode: f337
arrows-alt-v:
  changes:
    - 5.0.0
  label: Alternate Arrows Vertical
  search:
    terms:
      - resize
      - arrows-v
  styles:
    - solid
  unicode: f338
assistive-listening-systems:
  changes:
    - '4.6'
    - 5.0.0
  label: Assistive Listening Systems
  search:
    terms: []
  styles:
    - solid
  unicode: f2a2
asterisk:
  changes:
    - '1'
    - 5.0.0
  label: asterisk
  search:
    terms:
      - details
  styles:
    - solid
  unicode: f069
asymmetrik:
  changes:
    - 5.0.0
  label: 'Asymmetrik, Ltd.'
  search:
    terms: []
  sponsored:
    label: 'Asymmetrik, Ltd.'
    url: 'http://asymmetrik.com'
  styles:
    - brands
  unicode: f372
at:
  changes:
    - '4.2'
    - 5.0.0
  label: At
  search:
    terms:
      - email
      - e-mail
  styles:
    - solid
  unicode: f1fa
audible:
  changes:
    - 5.0.0
  label: Audible
  search:
    terms: []
  styles:
    - brands
  unicode: f373
audio-description:
  changes:
    - '4.6'
    - 5.0.0
  label: Audio Description
  search:
    terms: []
  styles:
    - solid
  unicode: f29e
autoprefixer:
  changes:
    - 5.0.0
  label: Autoprefixer
  search:
    terms: []
  styles:
    - brands
  unicode: f41c
avianex:
  changes:
    - 5.0.0
  label: avianex
  search:
    terms: []
  sponsored:
    label: avianex
    url: 'https://www.avianex.de'
  styles:
    - brands
  unicode: f374
aviato:
  changes:
    - 5.0.0
  label: Aviato
  search:
    terms: []
  styles:
    - brands
  unicode: f421
aws:
  changes:
    - 5.0.0
  label: Amazon Web Services (AWS)
  search:
    terms: []
  styles:
    - brands
  unicode: f375
backward:
  changes:
    - '1'
    - 5.0.0
  label: backward
  search:
    terms:
      - rewind
      - previous
  styles:
    - solid
  unicode: f04a
balance-scale:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.13
  label: Balance Scale
  search:
    terms:
      - weight
      - measure
      - justice
      - legal
      - balanced
  styles:
    - solid
  unicode: f24e
ban:
  changes:
    - '1'
    - 5.0.0
  label: ban
  search:
    terms:
      - delete
      - remove
      - trash
      - hide
      - block
      - stop
      - abort
      - cancel
      - ban
      - prohibit
  styles:
    - solid
  unicode: f05e
band-aid:
  changes:
    - 5.0.7
  label: Band-Aid
  search:
    terms:
      - bandage
      - ouch
      - boo boo
  styles:
    - solid
  unicode: f462
bandcamp:
  changes:
    - '4.7'
    - 5.0.0
  label: Bandcamp
  search:
    terms: []
  styles:
    - brands
  unicode: f2d5
barcode:
  changes:
    - '1'
    - 5.0.0
  label: barcode
  search:
    terms:
      - scan
  styles:
    - solid
  unicode: f02a
bars:
  changes:
    - '2'
    - 5.0.0
  label: Bars
  search:
    terms:
      - menu
      - drag
      - reorder
      - settings
      - list
      - ul
      - ol
      - checklist
      - todo
      - hamburger
      - navigation
      - nav
  styles:
    - solid
  unicode: f0c9
baseball-ball:
  changes:
    - 5.0.5
  label: Baseball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f433
basketball-ball:
  changes:
    - 5.0.5
  label: Basketball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f434
bath:
  changes:
    - '4.7'
    - 5.0.0
  label: Bath
  search:
    terms: []
  styles:
    - solid
  unicode: f2cd
battery-empty:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery Empty
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f244
battery-full:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery Full
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f240
battery-half:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery 1/2 Full
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f242
battery-quarter:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery 1/4 Full
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f243
battery-three-quarters:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery 3/4 Full
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f241
bed:
  changes:
    - '4.3'
    - 5.0.0
  label: Bed
  search:
    terms:
      - travel
  styles:
    - solid
  unicode: f236
beer:
  changes:
    - '3'
    - 5.0.0
  label: beer
  search:
    terms:
      - alcohol
      - stein
      - drink
      - mug
      - bar
      - liquor
  styles:
    - solid
  unicode: f0fc
behance:
  changes:
    - '4.1'
    - 5.0.0
  label: Behance
  search:
    terms: []
  styles:
    - brands
  unicode: f1b4
behance-square:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.3
  label: Behance Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1b5
bell:
  changes:
    - '2'
    - 5.0.0
  label: bell
  search:
    terms:
      - alert
      - reminder
      - notification
  styles:
    - solid
    - regular
  unicode: f0f3
bell-slash:
  changes:
    - '4.2'
    - 5.0.0
  label: Bell Slash
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1f6
bicycle:
  changes:
    - '4.2'
    - 5.0.0
  label: Bicycle
  search:
    terms:
      - vehicle
      - transportation
      - bike
      - gears
  styles:
    - solid
  unicode: f206
bimobject:
  changes:
    - 5.0.0
  label: BIMobject
  search:
    terms: []
  sponsored:
    label: BIMobject
    url: 'http://bimobject.com'
  styles:
    - brands
  unicode: f378
binoculars:
  changes:
    - '4.2'
    - 5.0.0
  label: Binoculars
  search:
    terms: []
  styles:
    - solid
  unicode: f1e5
birthday-cake:
  changes:
    - '4.2'
    - 5.0.0
  label: Birthday Cake
  search:
    terms: []
  styles:
    - solid
  unicode: f1fd
bitbucket:
  changes:
    - '3.2'
    - 5.0.0
  label: Bitbucket
  search:
    terms:
      - git
      - bitbucket-square
  styles:
    - brands
  unicode: f171
bitcoin:
  changes:
    - 5.0.0
  label: Bitcoin
  search:
    terms: []
  styles:
    - brands
  unicode: f379
bity:
  changes:
    - 5.0.0
  label: Bity
  search:
    terms: []
  sponsored:
    label: Bity
    url: 'http://bity.com'
  styles:
    - brands
  unicode: f37a
black-tie:
  changes:
    - '4.4'
    - 5.0.0
  label: Font Awesome Black Tie
  search:
    terms: []
  styles:
    - brands
  unicode: f27e
blackberry:
  changes:
    - 5.0.0
  label: BlackBerry
  search:
    terms: []
  styles:
    - brands
  unicode: f37b
blender:
  changes:
    - 5.0.13
  label: Blender
  search:
    terms: []
  styles:
    - solid
  unicode: f517
blind:
  changes:
    - '4.6'
    - 5.0.0
  label: Blind
  search:
    terms: []
  styles:
    - solid
  unicode: f29d
blogger:
  changes:
    - 5.0.0
  label: Blogger
  search:
    terms: []
  styles:
    - brands
  unicode: f37c
blogger-b:
  changes:
    - 5.0.0
  label: Blogger B
  search:
    terms: []
  styles:
    - brands
  unicode: f37d
bluetooth:
  changes:
    - '4.5'
    - 5.0.0
  label: Bluetooth
  search:
    terms: []
  styles:
    - brands
  unicode: f293
bluetooth-b:
  changes:
    - '4.5'
    - 5.0.0
  label: Bluetooth
  search:
    terms: []
  styles:
    - brands
  unicode: f294
bold:
  changes:
    - '1'
    - 5.0.0
  label: bold
  search:
    terms: []
  styles:
    - solid
  unicode: f032
bolt:
  changes:
    - '2'
    - 5.0.0
  label: Lightning Bolt
  search:
    terms:
      - lightning
      - weather
  styles:
    - solid
  unicode: f0e7
bomb:
  changes:
    - '4.1'
    - 5.0.0
  label: Bomb
  search:
    terms: []
  styles:
    - solid
  unicode: f1e2
book:
  changes:
    - '1'
    - 5.0.0
  label: book
  search:
    terms:
      - read
      - documentation
  styles:
    - solid
  unicode: f02d
book-open:
  changes:
    - 5.0.13
  label: Book Open
  search:
    terms:
      - open book
      - reading
  styles:
    - solid
  unicode: f518
bookmark:
  changes:
    - '1'
    - 5.0.0
  label: bookmark
  search:
    terms:
      - save
  styles:
    - solid
    - regular
  unicode: f02e
bowling-ball:
  changes:
    - 5.0.5
  label: Bowling Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f436
box:
  changes:
    - 5.0.7
  label: Box
  search:
    terms:
      - package
  styles:
    - solid
  unicode: f466
box-open:
  changes:
    - 5.0.9
  label: Box Open
  search:
    terms: []
  styles:
    - solid
  unicode: f49e
boxes:
  changes:
    - 5.0.7
  label: Boxes
  search:
    terms: []
  styles:
    - solid
  unicode: f468
braille:
  changes:
    - '4.6'
    - 5.0.0
  label: Braille
  search:
    terms: []
  styles:
    - solid
  unicode: f2a1
briefcase:
  changes:
    - '2'
    - 5.0.0
  label: Briefcase
  search:
    terms:
      - work
      - business
      - office
      - luggage
      - bag
  styles:
    - solid
  unicode: f0b1
briefcase-medical:
  changes:
    - 5.0.7
  label: Medical Briefcase
  search:
    terms:
      - health briefcase
  styles:
    - solid
  unicode: f469
broadcast-tower:
  changes:
    - 5.0.13
  label: Broadcast Tower
  search:
    terms:
      - waves
      - radio
      - airwaves
  styles:
    - solid
  unicode: f519
broom:
  changes:
    - 5.0.13
  label: Broom
  search:
    terms: []
  styles:
    - solid
  unicode: f51a
btc:
  changes:
    - '3.2'
    - 5.0.0
  label: BTC
  search:
    terms: []
  styles:
    - brands
  unicode: f15a
bug:
  changes:
    - '3.2'
    - 5.0.0
  label: Bug
  search:
    terms:
      - report
      - insect
  styles:
    - solid
  unicode: f188
building:
  changes:
    - '4.1'
    - 5.0.0
  label: Building
  search:
    terms:
      - work
      - business
      - apartment
      - office
      - company
  styles:
    - solid
    - regular
  unicode: f1ad
bullhorn:
  changes:
    - '2'
    - 5.0.0
  label: bullhorn
  search:
    terms:
      - announcement
      - share
      - broadcast
      - louder
      - megaphone
  styles:
    - solid
  unicode: f0a1
bullseye:
  changes:
    - '3.1'
    - 5.0.0
  label: Bullseye
  search:
    terms:
      - target
  styles:
    - solid
  unicode: f140
burn:
  changes:
    - 5.0.7
  label: Burn
  search:
    terms:
      - energy
  styles:
    - solid
  unicode: f46a
buromobelexperte:
  changes:
    - 5.0.0
  label: Büromöbel-Experte GmbH & Co. KG.
  search:
    terms: []
  sponsored:
    label: Büromöbel-Experte GmbH & Co. KG.
    url: 'https://www.bueromoebel-experte.de'
  styles:
    - brands
  unicode: f37f
bus:
  changes:
    - '4.2'
    - 5.0.0
  label: Bus
  search:
    terms:
      - vehicle
      - machine
      - public transportation
      - transportation
  styles:
    - solid
  unicode: f207
buysellads:
  changes:
    - '4.3'
    - 5.0.0
  label: BuySellAds
  search:
    terms: []
  styles:
    - brands
  unicode: f20d
calculator:
  changes:
    - '4.2'
    - 5.0.0
  label: Calculator
  search:
    terms: []
  styles:
    - solid
  unicode: f1ec
calendar:
  changes:
    - '3.1'
    - 5.0.0
  label: Calendar
  search:
    terms:
      - date
      - time
      - when
      - event
      - calendar-o
      - schedule
  styles:
    - solid
    - regular
  unicode: f133
calendar-alt:
  changes:
    - '1'
    - 5.0.0
  label: Alternate Calendar
  search:
    terms:
      - date
      - time
      - when
      - event
      - calendar
      - schedule
  styles:
    - solid
    - regular
  unicode: f073
calendar-check:
  changes:
    - '4.4'
    - 5.0.0
  label: Calendar Check
  search:
    terms:
      - todo
      - done
      - agree
      - accept
      - confirm
      - ok
      - select
      - success
      - appointment
      - correct
  styles:
    - solid
    - regular
  unicode: f274
calendar-minus:
  changes:
    - '4.4'
    - 5.0.0
  label: Calendar Minus
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f272
calendar-plus:
  changes:
    - '4.4'
    - 5.0.0
  label: Calendar Plus
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f271
calendar-times:
  changes:
    - '4.4'
    - 5.0.0
  label: Calendar Times
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f273
camera:
  changes:
    - '1'
    - 5.0.0
  label: camera
  search:
    terms:
      - photo
      - picture
      - record
  styles:
    - solid
  unicode: f030
camera-retro:
  changes:
    - '1'
    - 5.0.0
  label: Retro Camera
  search:
    terms:
      - photo
      - picture
      - record
  styles:
    - solid
  unicode: f083
capsules:
  changes:
    - 5.0.7
  label: Capsules
  search:
    terms:
      - medicine
      - drugs
  styles:
    - solid
  unicode: f46b
car:
  changes:
    - '4.1'
    - 5.0.0
  label: Car
  search:
    terms:
      - vehicle
      - machine
      - transportation
  styles:
    - solid
  unicode: f1b9
caret-down:
  changes:
    - '2'
    - 5.0.0
  label: Caret Down
  search:
    terms:
      - more
      - dropdown
      - menu
      - triangle down
      - arrow
  styles:
    - solid
  unicode: f0d7
caret-left:
  changes:
    - '2'
    - 5.0.0
  label: Caret Left
  search:
    terms:
      - previous
      - back
      - triangle left
      - arrow
  styles:
    - solid
  unicode: f0d9
caret-right:
  changes:
    - '2'
    - 5.0.0
  label: Caret Right
  search:
    terms:
      - next
      - forward
      - triangle right
      - arrow
  styles:
    - solid
  unicode: f0da
caret-square-down:
  changes:
    - '3.2'
    - 5.0.0
  label: Caret Square Down
  search:
    terms:
      - more
      - dropdown
      - menu
      - caret-square-o-down
  styles:
    - solid
    - regular
  unicode: f150
caret-square-left:
  changes:
    - '4'
    - 5.0.0
  label: Caret Square Left
  search:
    terms:
      - previous
      - back
      - caret-square-o-left
  styles:
    - solid
    - regular
  unicode: f191
caret-square-right:
  changes:
    - '3.2'
    - 5.0.0
  label: Caret Square Right
  search:
    terms:
      - next
      - forward
      - caret-square-o-right
  styles:
    - solid
    - regular
  unicode: f152
caret-square-up:
  changes:
    - '3.2'
    - 5.0.0
  label: Caret Square Up
  search:
    terms:
      - caret-square-o-up
  styles:
    - solid
    - regular
  unicode: f151
caret-up:
  changes:
    - '2'
    - 5.0.0
  label: Caret Up
  search:
    terms:
      - triangle up
      - arrow
  styles:
    - solid
  unicode: f0d8
cart-arrow-down:
  changes:
    - '4.3'
    - 5.0.0
  label: Shopping Cart Arrow Down
  search:
    terms:
      - shopping
  styles:
    - solid
  unicode: f218
cart-plus:
  changes:
    - '4.3'
    - 5.0.0
  label: Add to Shopping Cart
  search:
    terms:
      - add
      - shopping
  styles:
    - solid
  unicode: f217
cc-amazon-pay:
  changes:
    - 5.0.2
  label: Amazon Pay Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f42d
cc-amex:
  changes:
    - '4.2'
    - 5.0.0
  label: American Express Credit Card
  search:
    terms:
      - amex
  styles:
    - brands
  unicode: f1f3
cc-apple-pay:
  changes:
    - 5.0.0
  label: Apple Pay Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f416
cc-diners-club:
  changes:
    - '4.4'
    - 5.0.0
  label: Diner's Club Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f24c
cc-discover:
  changes:
    - '4.2'
    - 5.0.0
  label: Discover Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f2
cc-jcb:
  changes:
    - '4.4'
    - 5.0.0
  label: JCB Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f24b
cc-mastercard:
  changes:
    - '4.2'
    - 5.0.0
  label: MasterCard Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f1
cc-paypal:
  changes:
    - '4.2'
    - 5.0.0
  label: Paypal Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f4
cc-stripe:
  changes:
    - '4.2'
    - 5.0.0
  label: Stripe Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f5
cc-visa:
  changes:
    - '4.2'
    - 5.0.0
  label: Visa Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f0
centercode:
  changes:
    - 5.0.0
  label: Centercode
  search:
    terms: []
  sponsored:
    label: Centercode
    url: 'https://www.centercode.com'
  styles:
    - brands
  unicode: f380
certificate:
  changes:
    - '2'
    - 5.0.0
  label: certificate
  search:
    terms:
      - badge
      - star
  styles:
    - solid
  unicode: f0a3
chalkboard:
  changes:
    - 5.0.13
  label: Chalkboard
  search:
    terms:
      - school
      - learning
      - blackboard
      - whiteboard
      - writing
      - teaching
  styles:
    - solid
  unicode: f51b
chalkboard-teacher:
  changes:
    - 5.0.13
  label: Chalkboard Teacher
  search:
    terms:
      - school
      - learning
      - blackboard
      - whiteboard
      - writing
      - instructor
      - professor
  styles:
    - solid
  unicode: f51c
chart-area:
  changes:
    - '4.2'
    - 5.0.0
  label: Area Chart
  search:
    terms:
      - graph
      - analytics
      - area-chart
  styles:
    - solid
  unicode: f1fe
chart-bar:
  changes:
    - '1'
    - 5.0.0
  label: Bar Chart
  search:
    terms:
      - graph
      - analytics
      - bar-chart
  styles:
    - solid
    - regular
  unicode: f080
chart-line:
  changes:
    - '4.2'
    - 5.0.0
  label: Line Chart
  search:
    terms:
      - graph
      - analytics
      - line-chart
      - dashboard
  styles:
    - solid
  unicode: f201
chart-pie:
  changes:
    - '4.2'
    - 5.0.0
  label: Pie Chart
  search:
    terms:
      - graph
      - analytics
      - pie-chart
  styles:
    - solid
  unicode: f200
check:
  changes:
    - '1'
    - 5.0.0
  label: Check
  search:
    terms:
      - checkmark
      - done
      - todo
      - agree
      - accept
      - confirm
      - tick
      - ok
      - select
      - success
      - notification
      - notify
      - notice
      - 'yes'
      - correct
  styles:
    - solid
  unicode: f00c
check-circle:
  changes:
    - '1'
    - 5.0.0
  label: Check Circle
  search:
    terms:
      - todo
      - done
      - agree
      - accept
      - confirm
      - ok
      - select
      - success
      - 'yes'
      - correct
  styles:
    - solid
    - regular
  unicode: f058
check-square:
  changes:
    - '3.1'
    - 5.0.0
  label: Check Square
  search:
    terms:
      - checkmark
      - done
      - todo
      - agree
      - accept
      - confirm
      - ok
      - select
      - success
      - 'yes'
      - correct
  styles:
    - solid
    - regular
  unicode: f14a
chess:
  changes:
    - 5.0.5
  label: Chess
  search:
    terms: []
  styles:
    - solid
  unicode: f439
chess-bishop:
  changes:
    - 5.0.5
  label: Chess Bishop
  search:
    terms: []
  styles:
    - solid
  unicode: f43a
chess-board:
  changes:
    - 5.0.5
  label: Chess Board
  search:
    terms: []
  styles:
    - solid
  unicode: f43c
chess-king:
  changes:
    - 5.0.5
  label: Chess King
  search:
    terms: []
  styles:
    - solid
  unicode: f43f
chess-knight:
  changes:
    - 5.0.5
  label: Chess Knight
  search:
    terms: []
  styles:
    - solid
  unicode: f441
chess-pawn:
  changes:
    - 5.0.5
  label: Chess Pawn
  search:
    terms: []
  styles:
    - solid
  unicode: f443
chess-queen:
  changes:
    - 5.0.5
  label: Chess Queen
  search:
    terms: []
  styles:
    - solid
  unicode: f445
chess-rook:
  changes:
    - 5.0.5
  label: Chess Rook
  search:
    terms: []
  styles:
    - solid
  unicode: f447
chevron-circle-down:
  changes:
    - '3.1'
    - 5.0.0
  label: Chevron Circle Down
  search:
    terms:
      - more
      - dropdown
      - menu
      - arrow
  styles:
    - solid
  unicode: f13a
chevron-circle-left:
  changes:
    - '3.1'
    - 5.0.0
  label: Chevron Circle Left
  search:
    terms:
      - previous
      - back
      - arrow
  styles:
    - solid
  unicode: f137
chevron-circle-right:
  changes:
    - '3.1'
    - 5.0.0
  label: Chevron Circle Right
  search:
    terms:
      - next
      - forward
      - arrow
  styles:
    - solid
  unicode: f138
chevron-circle-up:
  changes:
    - '3.1'
    - 5.0.0
  label: Chevron Circle Up
  search:
    terms:
      - arrow
  styles:
    - solid
  unicode: f139
chevron-down:
  changes:
    - '1'
    - 5.0.0
  label: chevron-down
  search:
    terms: []
  styles:
    - solid
  unicode: f078
chevron-left:
  changes:
    - '1'
    - 5.0.0
  label: chevron-left
  search:
    terms:
      - bracket
      - previous
      - back
  styles:
    - solid
  unicode: f053
chevron-right:
  changes:
    - '1'
    - 5.0.0
  label: chevron-right
  search:
    terms:
      - bracket
      - next
      - forward
  styles:
    - solid
  unicode: f054
chevron-up:
  changes:
    - '1'
    - 5.0.0
  label: chevron-up
  search:
    terms: []
  styles:
    - solid
  unicode: f077
child:
  changes:
    - '4.1'
    - 5.0.0
  label: Child
  search:
    terms: []
  styles:
    - solid
  unicode: f1ae
chrome:
  changes:
    - '4.4'
    - 5.0.0
  label: Chrome
  search:
    terms:
      - browser
  styles:
    - brands
  unicode: f268
church:
  changes:
    - 5.0.13
  label: Church
  search:
    terms:
      - religion
      - building
      - community
  sponsored:
    label: Darren Wiebe
  styles:
    - solid
  unicode: f51d
circle:
  changes:
    - '3'
    - 5.0.0
  label: Circle
  search:
    terms:
      - dot
      - notification
      - circle-thin
  styles:
    - solid
    - regular
  unicode: f111
circle-notch:
  changes:
    - '4.1'
    - 5.0.0
  label: Circle Notched
  search:
    terms:
      - circle-o-notch
  styles:
    - solid
  unicode: f1ce
clipboard:
  changes:
    - 5.0.0
  label: Clipboard
  search:
    terms:
      - paste
  styles:
    - solid
    - regular
  unicode: f328
clipboard-check:
  changes:
    - 5.0.7
  label: Clipboard Check
  search:
    terms:
      - todo
      - done
      - agree
      - accept
      - confirm
      - ok
      - select
      - success
      - 'yes'
  styles:
    - solid
  unicode: f46c
clipboard-list:
  changes:
    - 5.0.7
  label: Clipboard List
  search:
    terms:
      - todo
      - ul
      - ol
      - checklist
      - finished
      - completed
      - done
      - schedule
      - intinerary
  styles:
    - solid
  unicode: f46d
clock:
  changes:
    - '1'
    - 5.0.0
  label: Clock
  search:
    terms:
      - watch
      - timer
      - late
      - timestamp
      - date
      - schedule
  styles:
    - solid
    - regular
  unicode: f017
clone:
  changes:
    - '4.4'
    - 5.0.0
  label: Clone
  search:
    terms:
      - copy
  styles:
    - solid
    - regular
  unicode: f24d
closed-captioning:
  changes:
    - '4.2'
    - 5.0.0
  label: Closed Captioning
  search:
    terms:
      - cc
  styles:
    - solid
    - regular
  unicode: f20a
cloud:
  changes:
    - '2'
    - 5.0.0
    - 5.0.11
  label: Cloud
  search:
    terms:
      - save
  styles:
    - solid
  unicode: f0c2
cloud-download-alt:
  changes:
    - 5.0.0
    - 5.0.11
  label: Alternate Cloud Download
  search:
    terms:
      - cloud-download
  styles:
    - solid
  unicode: f381
cloud-upload-alt:
  changes:
    - 5.0.0
    - 5.0.11
  label: Alternate Cloud Upload
  search:
    terms:
      - cloud-upload
  styles:
    - solid
  unicode: f382
cloudscale:
  changes:
    - 5.0.0
  label: cloudscale.ch
  search:
    terms: []
  sponsored:
    label: cloudscale.ch
    url: 'https://www.cloudscale.ch'
  styles:
    - brands
  unicode: f383
cloudsmith:
  changes:
    - 5.0.0
  label: Cloudsmith
  search:
    terms: []
  sponsored:
    label: Cloudsmith
    url: 'https://cloudsmith.io'
  styles:
    - brands
  unicode: f384
cloudversify:
  changes:
    - 5.0.0
  label: cloudversify
  search:
    terms: []
  sponsored:
    label: cloudversify
    url: 'https://www.cloudversify.com'
  styles:
    - brands
  unicode: f385
code:
  changes:
    - '3.1'
    - 5.0.0
  label: Code
  search:
    terms:
      - html
      - brackets
  styles:
    - solid
  unicode: f121
code-branch:
  changes:
    - 5.0.0
  label: Code Branch
  search:
    terms:
      - git
      - fork
      - vcs
      - svn
      - github
      - rebase
      - version
      - branch
      - code-fork
  styles:
    - solid
  unicode: f126
codepen:
  changes:
    - '4.1'
    - 5.0.0
  label: Codepen
  search:
    terms: []
  styles:
    - brands
  unicode: f1cb
codiepie:
  changes:
    - '4.5'
    - 5.0.0
  label: Codie Pie
  search:
    terms: []
  styles:
    - brands
  unicode: f284
coffee:
  changes:
    - '3'
    - 5.0.0
  label: Coffee
  search:
    terms:
      - morning
      - mug
      - breakfast
      - tea
      - drink
      - cafe
  styles:
    - solid
  unicode: f0f4
cog:
  changes:
    - '1'
    - 5.0.0
  label: cog
  search:
    terms:
      - settings
  styles:
    - solid
  unicode: f013
cogs:
  changes:
    - '1'
    - 5.0.0
  label: cogs
  search:
    terms:
      - settings
      - gears
  styles:
    - solid
  unicode: f085
coins:
  changes:
    - 5.0.13
  label: Coins
  search:
    terms: []
  styles:
    - solid
  unicode: f51e
columns:
  changes:
    - '2'
    - 5.0.0
  label: Columns
  search:
    terms:
      - split
      - panes
      - dashboard
  styles:
    - solid
  unicode: f0db
comment:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: comment
  search:
    terms:
      - speech
      - notification
      - note
      - chat
      - bubble
      - feedback
      - message
      - texting
      - sms
      - conversation
  styles:
    - solid
    - regular
  unicode: f075
comment-alt:
  changes:
    - '4.4'
    - 5.0.0
  label: Alternate Comment
  search:
    terms:
      - speech
      - notification
      - note
      - chat
      - bubble
      - feedback
      - message
      - texting
      - sms
      - conversation
      - commenting
      - commenting
  styles:
    - solid
    - regular
  unicode: f27a
comment-dots:
  changes:
    - 5.0.9
  label: Comment Dots
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f4ad
comment-slash:
  changes:
    - 5.0.9
  label: Comment Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4b3
comments:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: comments
  search:
    terms:
      - speech
      - notification
      - note
      - chat
      - bubble
      - feedback
      - message
      - texting
      - sms
      - conversation
  styles:
    - solid
    - regular
  unicode: f086
compact-disc:
  changes:
    - 5.0.13
  label: Compact Disc
  search:
    terms:
      - media
      - cd
      - disc
      - bluray
  styles:
    - solid
  unicode: f51f
compass:
  changes:
    - '3.2'
    - 5.0.0
  label: Compass
  search:
    terms:
      - safari
      - directory
      - menu
      - location
  styles:
    - solid
    - regular
  unicode: f14e
compress:
  changes:
    - 5.0.0
  label: Compress
  search:
    terms:
      - collapse
      - combine
      - contract
      - merge
      - smaller
  styles:
    - solid
  unicode: f066
connectdevelop:
  changes:
    - '4.3'
    - 5.0.0
  label: Connect Develop
  search:
    terms: []
  styles:
    - brands
  unicode: f20e
contao:
  changes:
    - '4.4'
    - 5.0.0
  label: Contao
  search:
    terms: []
  styles:
    - brands
  unicode: f26d
copy:
  changes:
    - '2'
    - 5.0.0
  label: Copy
  search:
    terms:
      - duplicate
      - clone
      - file
      - files-o
  styles:
    - solid
    - regular
  unicode: f0c5
copyright:
  changes:
    - '4.2'
    - 5.0.0
  label: Copyright
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1f9
couch:
  changes:
    - 5.0.9
  label: Couch
  search:
    terms: []
  styles:
    - solid
  unicode: f4b8
cpanel:
  changes:
    - 5.0.0
  label: cPanel
  search:
    terms: []
  sponsored:
    label: cPanel
    url: 'http://cpanel.com'
  styles:
    - brands
  unicode: f388
creative-commons:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.11
  label: Creative Commons
  search:
    terms: []
  styles:
    - brands
  unicode: f25e
creative-commons-by:
  changes:
    - 5.0.11
  label: Creative Commons Attribution
  search:
    terms: []
  styles:
    - brands
  unicode: f4e7
creative-commons-nc:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial
  search:
    terms: []
  styles:
    - brands
  unicode: f4e8
creative-commons-nc-eu:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial (Euro Sign)
  search:
    terms: []
  styles:
    - brands
  unicode: f4e9
creative-commons-nc-jp:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial (Yen Sign)
  search:
    terms: []
  styles:
    - brands
  unicode: f4ea
creative-commons-nd:
  changes:
    - 5.0.11
  label: Creative Commons No Derivative Works
  search:
    terms: []
  styles:
    - brands
  unicode: f4eb
creative-commons-pd:
  changes:
    - 5.0.11
  label: Creative Commons Public Domain
  search:
    terms: []
  styles:
    - brands
  unicode: f4ec
creative-commons-pd-alt:
  changes:
    - 5.0.11
  label: Creative Commons Public Domain Alternate
  search:
    terms: []
  styles:
    - brands
  unicode: f4ed
creative-commons-remix:
  changes:
    - 5.0.11
  label: Creative Commons Remix
  search:
    terms: []
  styles:
    - brands
  unicode: f4ee
creative-commons-sa:
  changes:
    - 5.0.11
  label: Creative Commons Share Alike
  search:
    terms: []
  styles:
    - brands
  unicode: f4ef
creative-commons-sampling:
  changes:
    - 5.0.11
  label: Creative Commons Sampling
  search:
    terms: []
  styles:
    - brands
  unicode: f4f0
creative-commons-sampling-plus:
  changes:
    - 5.0.11
  label: Creative Commons Sampling +
  search:
    terms: []
  styles:
    - brands
  unicode: f4f1
creative-commons-share:
  changes:
    - 5.0.11
  label: Creative Commons Share
  search:
    terms: []
  styles:
    - brands
  unicode: f4f2
credit-card:
  changes:
    - '2'
    - 5.0.0
  label: Credit Card
  search:
    terms:
      - money
      - buy
      - debit
      - checkout
      - purchase
      - payment
      - credit-card-alt
  styles:
    - solid
    - regular
  unicode: f09d
crop:
  changes:
    - '3.1'
    - 5.0.0
  label: crop
  search:
    terms:
      - design
  styles:
    - solid
  unicode: f125
crosshairs:
  changes:
    - '1'
    - 5.0.0
  label: Crosshairs
  search:
    terms:
      - picker
      - gpd
  styles:
    - solid
  unicode: f05b
crow:
  changes:
    - 5.0.13
  label: Crow
  search:
    terms:
      - bird
      - toad
      - bullfrog
  sponsored:
    label: Jon Galloway
  styles:
    - solid
  unicode: f520
crown:
  changes:
    - 5.0.13
  label: Crown
  search:
    terms: []
  styles:
    - solid
  unicode: f521
css3:
  changes:
    - '3.1'
    - 5.0.0
  label: CSS 3 Logo
  search:
    terms:
      - code
  styles:
    - brands
  unicode: f13c
css3-alt:
  changes:
    - 5.0.0
  label: Alternate CSS3 Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f38b
cube:
  changes:
    - '4.1'
    - 5.0.0
  label: Cube
  search:
    terms:
      - package
  styles:
    - solid
  unicode: f1b2
cubes:
  changes:
    - '4.1'
    - 5.0.0
  label: Cubes
  search:
    terms:
      - packages
  styles:
    - solid
  unicode: f1b3
cut:
  changes:
    - '2'
    - 5.0.0
  label: Cut
  search:
    terms:
      - scissors
      - scissors
  styles:
    - solid
  unicode: f0c4
cuttlefish:
  changes:
    - 5.0.0
  label: Cuttlefish
  search:
    terms: []
  sponsored:
    label: Cuttlefish
    url: 'http://wearecuttlefish.com'
  styles:
    - brands
  unicode: f38c
d-and-d:
  changes:
    - 5.0.0
  label: Dungeons & Dragons
  search:
    terms: []
  styles:
    - brands
  unicode: f38d
dashcube:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
  label: DashCube
  search:
    terms: []
  styles:
    - brands
  unicode: f210
database:
  changes:
    - '4.1'
    - 5.0.0
  label: Database
  search:
    terms: []
  styles:
    - solid
  unicode: f1c0
deaf:
  changes:
    - '4.6'
    - 5.0.0
  label: Deaf
  search:
    terms: []
  styles:
    - solid
  unicode: f2a4
delicious:
  changes:
    - '4.1'
    - 5.0.0
  label: Delicious Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a5
deploydog:
  changes:
    - 5.0.0
  label: deploy.dog
  search:
    terms: []
  sponsored:
    label: deploy.dog
    url: 'http://deploy.dog'
  styles:
    - brands
  unicode: f38e
deskpro:
  changes:
    - 5.0.0
  label: Deskpro
  search:
    terms: []
  sponsored:
    label: Deskpro
    url: 'http://www.deskpro.com'
  styles:
    - brands
  unicode: f38f
desktop:
  changes:
    - '3'
    - 5.0.0
  label: Desktop
  search:
    terms:
      - monitor
      - screen
      - desktop
      - computer
      - demo
      - device
      - pc
      - machine
  styles:
    - solid
  unicode: f108
deviantart:
  changes:
    - '4.1'
    - 5.0.0
  label: deviantART
  search:
    terms: []
  styles:
    - brands
  unicode: f1bd
diagnoses:
  changes:
    - 5.0.7
  label: Diagnoses
  search:
    terms: []
  styles:
    - solid
  unicode: f470
dice:
  changes:
    - 5.0.13
  label: Dice
  search:
    terms:
      - gambling
      - roll
      - game
      - chance
  styles:
    - solid
  unicode: f522
dice-five:
  changes:
    - 5.0.13
  label: Dice Five
  search:
    terms:
      - gambling
      - roll
      - game
      - chance
  styles:
    - solid
  unicode: f523
dice-four:
  changes:
    - 5.0.13
  label: Dice Four
  search:
    terms:
      - gambling
      - roll
      - game
      - chance
  styles:
    - solid
  unicode: f524
dice-one:
  changes:
    - 5.0.13
  label: Dice One
  search:
    terms:
      - gambling
      - roll
      - game
      - chance
  styles:
    - solid
  unicode: f525
dice-six:
  changes:
    - 5.0.13
  label: Dice Six
  search:
    terms:
      - gambling
      - roll
      - game
      - chance
  styles:
    - solid
  unicode: f526
dice-three:
  changes:
    - 5.0.13
  label: Dice Three
  search:
    terms:
      - gambling
      - roll
      - game
      - chance
  styles:
    - solid
  unicode: f527
dice-two:
  changes:
    - 5.0.13
  label: Dice Two
  search:
    terms:
      - gambling
      - roll
      - game
      - chance
  styles:
    - solid
  unicode: f528
digg:
  changes:
    - '4.1'
    - 5.0.0
  label: Digg Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a6
digital-ocean:
  changes:
    - 5.0.0
  label: Digital Ocean
  search:
    terms: []
  styles:
    - brands
  unicode: f391
discord:
  changes:
    - 5.0.0
  label: Discord
  search:
    terms: []
  styles:
    - brands
  unicode: f392
discourse:
  changes:
    - 5.0.0
    - 5.0.3
  label: Discourse
  search:
    terms: []
  sponsored:
    label: Discourse
    url: 'https://discourse.org'
  styles:
    - brands
  unicode: f393
divide:
  changes:
    - 5.0.13
  label: Divide
  search:
    terms: []
  styles:
    - solid
  unicode: f529
dna:
  changes:
    - 5.0.7
    - 5.0.10
  label: DNA
  search:
    terms:
      - double helix
      - helix
  styles:
    - solid
  unicode: f471
dochub:
  changes:
    - 5.0.0
  label: DocHub
  search:
    terms: []
  sponsored:
    label: DocHub
    url: 'https://dochub.com'
  styles:
    - brands
  unicode: f394
docker:
  changes:
    - 5.0.0
  label: Docker
  search:
    terms: []
  styles:
    - brands
  unicode: f395
dollar-sign:
  changes:
    - '3.2'
    - 5.0.0
    - 5.0.9
  label: Dollar Sign
  search:
    terms:
      - usd
      - price
  styles:
    - solid
  unicode: f155
dolly:
  changes:
    - 5.0.7
  label: Dolly
  search:
    terms: []
  styles:
    - solid
  unicode: f472
dolly-flatbed:
  changes:
    - 5.0.7
  label: Dolly Flatbed
  search:
    terms: []
  styles:
    - solid
  unicode: f474
donate:
  changes:
    - 5.0.9
  label: Donate
  search:
    terms:
      - give
      - generosity
  styles:
    - solid
  unicode: f4b9
door-closed:
  changes:
    - 5.0.13
  label: Door Closed
  search:
    terms: []
  styles:
    - solid
  unicode: f52a
door-open:
  changes:
    - 5.0.13
  label: Door Open
  search:
    terms: []
  styles:
    - solid
  unicode: f52b
dot-circle:
  changes:
    - '4'
    - 5.0.0
  label: Dot Circle
  search:
    terms:
      - target
      - bullseye
      - notification
  styles:
    - solid
    - regular
  unicode: f192
dove:
  changes:
    - 5.0.9
  label: Dove
  search:
    terms: []
  styles:
    - solid
  unicode: f4ba
download:
  changes:
    - '1'
    - 5.0.0
  label: Download
  search:
    terms:
      - import
  styles:
    - solid
  unicode: f019
draft2digital:
  changes:
    - 5.0.0
  label: Draft2digital
  search:
    terms: []
  sponsored:
    label: Draft2Digital
    url: 'http://draft2digital.com'
  styles:
    - brands
  unicode: f396
dribbble:
  changes:
    - 5.0.0
  label: Dribbble
  search:
    terms: []
  styles:
    - brands
  unicode: f17d
dribbble-square:
  changes:
    - 5.0.0
  label: Dribbble Square
  search:
    terms: []
  styles:
    - brands
  unicode: f397
dropbox:
  changes:
    - '3.2'
    - 5.0.0
    - 5.0.1
  label: Dropbox
  search:
    terms: []
  styles:
    - brands
  unicode: f16b
drupal:
  changes:
    - '4.1'
    - 5.0.0
  label: Drupal Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a9
dumbbell:
  changes:
    - 5.0.5
  label: Dumbbell
  search:
    terms: []
  styles:
    - solid
  unicode: f44b
dyalog:
  changes:
    - 5.0.0
  label: Dyalog
  search:
    terms: []
  sponsored:
    label: Dyalog APL
    url: 'http://www.dyalog.com'
  styles:
    - brands
  unicode: f399
earlybirds:
  changes:
    - 5.0.0
  label: Earlybirds
  search:
    terms: []
  styles:
    - brands
  unicode: f39a
ebay:
  changes:
    - 5.0.11
  label: eBay
  search:
    terms: []
  styles:
    - brands
  unicode: f4f4
edge:
  changes:
    - '4.5'
    - 5.0.0
  label: Edge Browser
  search:
    terms:
      - browser
      - ie
  styles:
    - brands
  unicode: f282
edit:
  changes:
    - '1'
    - 5.0.0
  label: Edit
  search:
    terms:
      - write
      - edit
      - update
      - pencil
      - pen
  styles:
    - solid
    - regular
  unicode: f044
eject:
  changes:
    - '1'
    - 5.0.0
  label: eject
  search:
    terms: []
  styles:
    - solid
  unicode: f052
elementor:
  changes:
    - 5.0.3
  label: Elementor
  search:
    terms: []
  styles:
    - brands
  unicode: f430
ellipsis-h:
  changes:
    - '3.1'
    - 5.0.0
  label: Horizontal Ellipsis
  search:
    terms:
      - dots
      - menu
      - drag
      - reorder
      - settings
      - list
      - ul
      - ol
      - kebab
      - navigation
      - nav
  styles:
    - solid
  unicode: f141
ellipsis-v:
  changes:
    - '3.1'
    - 5.0.0
  label: Vertical Ellipsis
  search:
    terms:
      - dots
      - menu
      - drag
      - reorder
      - settings
      - list
      - ul
      - ol
      - kebab
      - navigation
      - nav
  styles:
    - solid
  unicode: f142
ember:
  changes:
    - 5.0.0
    - 5.0.3
  label: Ember
  search:
    terms: []
  styles:
    - brands
  unicode: f423
empire:
  changes:
    - '4.1'
    - 5.0.0
  label: Galactic Empire
  search:
    terms: []
  styles:
    - brands
  unicode: f1d1
envelope:
  changes:
    - '2'
    - 5.0.0
  label: Envelope
  search:
    terms:
      - email
      - e-mail
      - letter
      - support
      - mail
      - message
      - notification
  styles:
    - solid
    - regular
  unicode: f0e0
envelope-open:
  changes:
    - '4.7'
    - 5.0.0
  label: Envelope Open
  search:
    terms:
      - email
      - e-mail
      - letter
      - support
      - mail
      - message
      - notification
  styles:
    - solid
    - regular
  unicode: f2b6
envelope-square:
  changes:
    - '4.1'
    - 5.0.0
  label: Envelope Square
  search:
    terms:
      - email
      - e-mail
      - letter
      - support
      - mail
      - message
      - notification
  styles:
    - solid
  unicode: f199
envira:
  changes:
    - '4.6'
    - 5.0.0
  label: Envira Gallery
  search:
    terms:
      - leaf
  styles:
    - brands
  unicode: f299
equals:
  changes:
    - 5.0.13
  label: Equals
  search:
    terms: []
  styles:
    - solid
  unicode: f52c
eraser:
  changes:
    - '3.1'
    - 5.0.0
  label: eraser
  search:
    terms:
      - remove
      - delete
  styles:
    - solid
  unicode: f12d
erlang:
  changes:
    - 5.0.0
    - 5.0.3
  label: Erlang
  search:
    terms: []
  styles:
    - brands
  unicode: f39d
ethereum:
  changes:
    - 5.0.2
  label: Ethereum
  search:
    terms: []
  styles:
    - brands
  unicode: f42e
etsy:
  changes:
    - '4.7'
    - 5.0.0
  label: Etsy
  search:
    terms: []
  styles:
    - brands
  unicode: f2d7
euro-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Euro Sign
  search:
    terms:
      - eur
      - eur
  styles:
    - solid
  unicode: f153
exchange-alt:
  changes:
    - 5.0.0
  label: Alternate Exchange
  search:
    terms:
      - transfer
      - arrows
      - arrow
      - exchange
      - swap
      - return
      - reciprocate
  styles:
    - solid
  unicode: f362
exclamation:
  changes:
    - '3.1'
    - 5.0.0
  label: exclamation
  search:
    terms:
      - warning
      - error
      - problem
      - notification
      - notify
      - notice
      - alert
      - danger
  styles:
    - solid
  unicode: f12a
exclamation-circle:
  changes:
    - '1'
    - 5.0.0
  label: Exclamation Circle
  search:
    terms:
      - warning
      - error
      - problem
      - notification
      - notify
      - notice
      - alert
      - danger
  styles:
    - solid
  unicode: f06a
exclamation-triangle:
  changes:
    - '1'
    - 5.0.0
  label: Exclamation Triangle
  search:
    terms:
      - warning
      - error
      - problem
      - notification
      - notify
      - notice
      - alert
      - danger
  styles:
    - solid
  unicode: f071
expand:
  changes:
    - 5.0.0
  label: Expand
  search:
    terms:
      - enlarge
      - bigger
      - resize
  styles:
    - solid
  unicode: f065
expand-arrows-alt:
  changes:
    - 5.0.0
  label: Alternate Expand Arrows
  search:
    terms:
      - enlarge
      - bigger
      - resize
      - move
      - arrows-alt
  styles:
    - solid
  unicode: f31e
expeditedssl:
  changes:
    - '4.4'
    - 5.0.0
  label: ExpeditedSSL
  search:
    terms: []
  styles:
    - brands
  unicode: f23e
external-link-alt:
  changes:
    - 5.0.0
  label: Alternate External Link
  search:
    terms:
      - open
      - new
      - external-link
  styles:
    - solid
  unicode: f35d
external-link-square-alt:
  changes:
    - 5.0.0
  label: Alternate External Link Square
  search:
    terms:
      - open
      - new
      - external-link-square
  styles:
    - solid
  unicode: f360
eye:
  changes:
    - '1'
    - 5.0.0
  label: Eye
  search:
    terms:
      - show
      - visible
      - views
      - see
      - seen
      - sight
      - optic
  styles:
    - solid
    - regular
  unicode: f06e
eye-dropper:
  changes:
    - '4.2'
    - 5.0.0
  label: Eye Dropper
  search:
    terms:
      - eyedropper
  styles:
    - solid
  unicode: f1fb
eye-slash:
  changes:
    - '1'
    - 5.0.0
  label: Eye Slash
  search:
    terms:
      - toggle
      - show
      - hide
      - visible
      - visiblity
      - views
      - unseen
      - blind
  styles:
    - solid
    - regular
  unicode: f070
facebook:
  changes:
    - '2'
    - 5.0.0
  label: Facebook
  search:
    terms:
      - social network
      - facebook-official
  styles:
    - brands
  unicode: f09a
facebook-f:
  changes:
    - 5.0.0
  label: Facebook F
  search:
    terms:
      - facebook
  styles:
    - brands
  unicode: f39e
facebook-messenger:
  changes:
    - 5.0.0
  label: Facebook Messenger
  search:
    terms: []
  styles:
    - brands
  unicode: f39f
facebook-square:
  changes:
    - '1'
    - 5.0.0
  label: Facebook Square
  search:
    terms:
      - social network
  styles:
    - brands
  unicode: f082
fast-backward:
  changes:
    - '1'
    - 5.0.0
  label: fast-backward
  search:
    terms:
      - rewind
      - previous
      - beginning
      - start
      - first
  styles:
    - solid
  unicode: f049
fast-forward:
  changes:
    - '1'
    - 5.0.0
  label: fast-forward
  search:
    terms:
      - next
      - end
      - last
  styles:
    - solid
  unicode: f050
fax:
  changes:
    - '4.1'
    - 5.0.0
  label: Fax
  search:
    terms: []
  styles:
    - solid
  unicode: f1ac
feather:
  changes:
    - 5.0.13
  label: Feather
  search:
    terms: []
  styles:
    - solid
  unicode: f52d
female:
  changes:
    - '3.2'
    - 5.0.0
  label: Female
  search:
    terms:
      - woman
      - human
      - user
      - person
      - profile
  styles:
    - solid
  unicode: f182
fighter-jet:
  changes:
    - '3'
    - 5.0.0
  label: fighter-jet
  search:
    terms:
      - fly
      - plane
      - airplane
      - quick
      - fast
      - travel
      - transportation
      - maverick
      - goose
      - top gun
  styles:
    - solid
  unicode: f0fb
file:
  changes:
    - '3.2'
    - 5.0.0
  label: File
  search:
    terms:
      - new
      - page
      - pdf
      - document
  styles:
    - solid
    - regular
  unicode: f15b
file-alt:
  changes:
    - '3.2'
    - 5.0.0
  label: Alternate File
  search:
    terms:
      - new
      - page
      - pdf
      - document
      - file-text
      - invoice
  styles:
    - solid
    - regular
  unicode: f15c
file-archive:
  changes:
    - '4.1'
    - 5.0.0
  label: Archive File
  search:
    terms:
      - zip
      - .zip
      - compress
      - compression
      - bundle
      - download
  styles:
    - solid
    - regular
  unicode: f1c6
file-audio:
  changes:
    - '4.1'
    - 5.0.0
  label: Audio File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c7
file-code:
  changes:
    - '4.1'
    - 5.0.0
  label: Code File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c9
file-excel:
  changes:
    - '4.1'
    - 5.0.0
  label: Excel File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c3
file-image:
  changes:
    - '4.1'
    - 5.0.0
  label: Image File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c5
file-medical:
  changes:
    - 5.0.7
  label: Medical File
  search:
    terms: []
  styles:
    - solid
  unicode: f477
file-medical-alt:
  changes:
    - 5.0.7
  label: Alternate Medical File
  search:
    terms: []
  styles:
    - solid
  unicode: f478
file-pdf:
  changes:
    - '4.1'
    - 5.0.0
  label: PDF File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c1
file-powerpoint:
  changes:
    - '4.1'
    - 5.0.0
  label: Powerpoint File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c4
file-video:
  changes:
    - '4.1'
    - 5.0.0
  label: Video File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c8
file-word:
  changes:
    - '4.1'
    - 5.0.0
  label: Word File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c2
film:
  changes:
    - '1'
    - 5.0.0
  label: Film
  search:
    terms:
      - movie
  styles:
    - solid
  unicode: f008
filter:
  changes:
    - '2'
    - 5.0.0
  label: Filter
  search:
    terms:
      - funnel
      - options
  styles:
    - solid
  unicode: f0b0
fire:
  changes:
    - '1'
    - 5.0.0
  label: fire
  search:
    terms:
      - flame
      - hot
      - popular
  styles:
    - solid
  unicode: f06d
fire-extinguisher:
  changes:
    - '3.1'
    - 5.0.0
  label: fire-extinguisher
  search:
    terms: []
  styles:
    - solid
  unicode: f134
firefox:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.1
  label: Firefox
  search:
    terms:
      - browser
  styles:
    - brands
  unicode: f269
first-aid:
  changes:
    - 5.0.7
  label: First Aid
  search:
    terms: []
  styles:
    - solid
  unicode: f479
first-order:
  changes:
    - '4.6'
    - 5.0.0
  label: First Order
  search:
    terms: []
  styles:
    - brands
  unicode: f2b0
first-order-alt:
  changes:
    - 5.0.12
  label: Alternate First Order
  search:
    terms: []
  styles:
    - brands
  unicode: f50a
firstdraft:
  changes:
    - 5.0.0
  label: firstdraft
  search:
    terms: []
  sponsored:
    label: firstdraft
    url: 'http://www.firstdraft.com'
  styles:
    - brands
  unicode: f3a1
flag:
  changes:
    - '1'
    - 5.0.0
  label: flag
  search:
    terms:
      - report
      - notification
      - notify
      - notice
  styles:
    - solid
    - regular
  unicode: f024
flag-checkered:
  changes:
    - '3.1'
    - 5.0.0
  label: flag-checkered
  search:
    terms:
      - report
      - notification
      - notify
      - notice
  styles:
    - solid
  unicode: f11e
flask:
  changes:
    - '2'
    - 5.0.0
  label: Flask
  search:
    terms:
      - science
      - beaker
      - experimental
      - labs
  styles:
    - solid
  unicode: f0c3
flickr:
  changes:
    - '3.2'
    - 5.0.0
  label: Flickr
  search:
    terms: []
  styles:
    - brands
  unicode: f16e
flipboard:
  changes:
    - 5.0.5
    - 5.0.9
  label: Flipboard
  search:
    terms: []
  styles:
    - brands
  unicode: f44d
fly:
  changes:
    - 5.0.0
  label: Fly
  search:
    terms: []
  styles:
    - brands
  unicode: f417
folder:
  changes:
    - '1'
    - 5.0.0
  label: Folder
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f07b
folder-open:
  changes:
    - '1'
    - 5.0.0
  label: Folder Open
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f07c
font:
  changes:
    - '1'
    - 5.0.0
  label: font
  search:
    terms:
      - text
  styles:
    - solid
  unicode: f031
font-awesome:
  changes:
    - '4.6'
    - 5.0.0
  label: Font Awesome
  search:
    terms:
      - meanpath
  styles:
    - brands
  unicode: f2b4
font-awesome-alt:
  changes:
    - 5.0.0
  label: Alternate Font Awesome
  search:
    terms: []
  styles:
    - brands
  unicode: f35c
font-awesome-flag:
  changes:
    - 5.0.0
    - 5.0.1
  label: Font Awesome Flag
  search:
    terms: []
  styles:
    - brands
  unicode: f425
font-awesome-logo-full:
  changes:
    - 5.0.11
  label: Font Awesome Full Logo
  ligatures:
    - Font Awesome
  search:
    terms: []
  styles:
    - regular
    - solid
    - brands
  unicode: f4e6
fonticons:
  changes:
    - '4.4'
    - 5.0.0
  label: Fonticons
  search:
    terms: []
  styles:
    - brands
  unicode: f280
fonticons-fi:
  changes:
    - 5.0.0
  label: Fonticons Fi
  search:
    terms: []
  styles:
    - brands
  unicode: f3a2
football-ball:
  changes:
    - 5.0.5
  label: Football Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f44e
fort-awesome:
  changes:
    - '4.5'
    - 5.0.0
    - 5.0.3
  label: Fort Awesome
  search:
    terms:
      - castle
  styles:
    - brands
  unicode: f286
fort-awesome-alt:
  changes:
    - 5.0.0
  label: Alternate Fort Awesome
  search:
    terms:
      - castle
  styles:
    - brands
  unicode: f3a3
forumbee:
  changes:
    - '4.3'
    - 5.0.0
  label: Forumbee
  search:
    terms: []
  styles:
    - brands
  unicode: f211
forward:
  changes:
    - '1'
    - 5.0.0
  label: forward
  search:
    terms:
      - forward
      - next
  styles:
    - solid
  unicode: f04e
foursquare:
  changes:
    - '3.2'
    - 5.0.0
  label: Foursquare
  search:
    terms: []
  styles:
    - brands
  unicode: f180
free-code-camp:
  changes:
    - '4.7'
    - 5.0.0
  label: Free Code Camp
  search:
    terms: []
  styles:
    - brands
  unicode: f2c5
freebsd:
  changes:
    - 5.0.0
  label: FreeBSD
  search:
    terms: []
  styles:
    - brands
  unicode: f3a4
frog:
  changes:
    - 5.0.13
  label: Frog
  search:
    terms:
      - kermit
      - wart
      - prince
      - kiss
      - toad
      - bullfrog
  sponsored:
    label: Max Elman
  styles:
    - solid
  unicode: f52e
frown:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.9
  label: Frown
  search:
    terms:
      - face
      - emoticon
      - sad
      - disapprove
      - rating
  styles:
    - solid
    - regular
  unicode: f119
fulcrum:
  changes:
    - 5.0.12
  label: Fulcrum
  search:
    terms: []
  styles:
    - brands
  unicode: f50b
futbol:
  changes:
    - '4.2'
    - 5.0.0
    - 5.0.5
  label: Futbol
  search:
    terms:
      - soccer
      - football
      - ball
  styles:
    - solid
    - regular
  unicode: f1e3
galactic-republic:
  changes:
    - 5.0.12
  label: Galactic Republic
  search:
    terms: []
  styles:
    - brands
  unicode: f50c
galactic-senate:
  changes:
    - 5.0.12
  label: Galactic Senate
  search:
    terms: []
  styles:
    - brands
  unicode: f50d
gamepad:
  changes:
    - '3.1'
    - 5.0.0
  label: Gamepad
  search:
    terms:
      - controller
  styles:
    - solid
  unicode: f11b
gas-pump:
  changes:
    - 5.0.13
  label: Gas Pump
  search:
    terms: []
  styles:
    - solid
  unicode: f52f
gavel:
  changes:
    - '2'
    - 5.0.0
  label: Gavel
  search:
    terms:
      - judge
      - lawyer
      - opinion
      - hammer
  styles:
    - solid
  unicode: f0e3
gem:
  changes:
    - 5.0.0
  label: Gem
  search:
    terms:
      - diamond
  styles:
    - solid
    - regular
  unicode: f3a5
genderless:
  changes:
    - '4.4'
    - 5.0.0
  label: Genderless
  search:
    terms: []
  styles:
    - solid
  unicode: f22d
get-pocket:
  changes:
    - '4.4'
    - 5.0.0
  label: Get Pocket
  search:
    terms: []
  styles:
    - brands
  unicode: f265
gg:
  changes:
    - '4.4'
    - 5.0.0
  label: GG Currency
  search:
    terms: []
  styles:
    - brands
  unicode: f260
gg-circle:
  changes:
    - '4.4'
    - 5.0.0
  label: GG Currency Circle
  search:
    terms: []
  styles:
    - brands
  unicode: f261
gift:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: gift
  search:
    terms:
      - present
      - party
      - wrapped
      - giving
      - generosity
  styles:
    - solid
  unicode: f06b
git:
  changes:
    - '4.1'
    - 5.0.0
  label: Git
  search:
    terms: []
  styles:
    - brands
  unicode: f1d3
git-square:
  changes:
    - '4.1'
    - 5.0.0
  label: Git Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1d2
github:
  changes:
    - '2'
    - 5.0.0
  label: GitHub
  search:
    terms:
      - octocat
  styles:
    - brands
  unicode: f09b
github-alt:
  changes:
    - '3'
    - 5.0.0
  label: Alternate GitHub
  search:
    terms:
      - octocat
  styles:
    - brands
  unicode: f113
github-square:
  changes:
    - '1'
    - 5.0.0
  label: GitHub Square
  search:
    terms:
      - octocat
  styles:
    - brands
  unicode: f092
gitkraken:
  changes:
    - 5.0.0
  label: GitKraken
  search:
    terms: []
  sponsored:
    label: GitKraken
    url: 'https://www.gitkraken.com'
  styles:
    - brands
  unicode: f3a6
gitlab:
  changes:
    - '4.6'
    - 5.0.0
  label: GitLab
  search:
    terms:
      - Axosoft
  styles:
    - brands
  unicode: f296
gitter:
  changes:
    - 5.0.0
  label: Gitter
  search:
    terms: []
  styles:
    - brands
  unicode: f426
glass-martini:
  changes:
    - '1'
    - 5.0.0
  label: Martini Glass
  search:
    terms:
      - martini
      - drink
      - bar
      - alcohol
      - liquor
      - glass
  styles:
    - solid
  unicode: f000
glasses:
  changes:
    - 5.0.13
  label: Glasses
  search:
    terms:
      - spectacles
      - reading
      - hipster
      - sight
      - foureyes
      - nerd
  styles:
    - solid
  unicode: f530
glide:
  changes:
    - '4.6'
    - 5.0.0
  label: Glide
  search:
    terms: []
  styles:
    - brands
  unicode: f2a5
glide-g:
  changes:
    - '4.6'
    - 5.0.0
  label: Glide G
  search:
    terms: []
  styles:
    - brands
  unicode: f2a6
globe:
  changes:
    - '2'
    - 5.0.0
    - 5.0.9
  label: Globe
  search:
    terms:
      - world
      - planet
      - map
      - place
      - travel
      - earth
      - global
      - translate
      - all
      - language
      - localize
      - location
      - coordinates
      - country
      - gps
      - online
  styles:
    - solid
  unicode: f0ac
gofore:
  changes:
    - 5.0.0
  label: Gofore
  search:
    terms: []
  sponsored:
    label: Gofore
    url: 'http://gofore.com'
  styles:
    - brands
  unicode: f3a7
golf-ball:
  changes:
    - 5.0.5
  label: Golf Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f450
goodreads:
  changes:
    - 5.0.0
  label: Goodreads
  search:
    terms: []
  styles:
    - brands
  unicode: f3a8
goodreads-g:
  changes:
    - 5.0.0
  label: Goodreads G
  search:
    terms: []
  styles:
    - brands
  unicode: f3a9
google:
  changes:
    - '4.1'
    - 5.0.0
  label: Google Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a0
google-drive:
  changes:
    - 5.0.0
  label: Google Drive
  search:
    terms: []
  styles:
    - brands
  unicode: f3aa
google-play:
  changes:
    - 5.0.0
  label: Google Play
  search:
    terms: []
  styles:
    - brands
  unicode: f3ab
google-plus:
  changes:
    - '4.6'
    - 5.0.0
  label: Google Plus
  search:
    terms:
      - google-plus-circle
      - google-plus-official
  styles:
    - brands
  unicode: f2b3
google-plus-g:
  changes:
    - '2'
    - 5.0.0
  label: Google Plus G
  search:
    terms:
      - social network
      - google-plus
  styles:
    - brands
  unicode: f0d5
google-plus-square:
  changes:
    - '2'
    - 5.0.0
  label: Google Plus Square
  search:
    terms:
      - social network
  styles:
    - brands
  unicode: f0d4
google-wallet:
  changes:
    - '4.2'
    - 5.0.0
  label: Google Wallet
  search:
    terms: []
  styles:
    - brands
  unicode: f1ee
graduation-cap:
  changes:
    - '4.1'
    - 5.0.0
  label: Graduation Cap
  search:
    terms:
      - learning
      - school
      - student
  styles:
    - solid
  unicode: f19d
gratipay:
  changes:
    - '3.2'
    - 5.0.0
  label: Gratipay (Gittip)
  search:
    terms:
      - heart
      - like
      - favorite
      - love
  styles:
    - brands
  unicode: f184
grav:
  changes:
    - '4.7'
    - 5.0.0
  label: Grav
  search:
    terms: []
  styles:
    - brands
  unicode: f2d6
greater-than:
  changes:
    - 5.0.13
  label: Greater Than
  search:
    terms: []
  styles:
    - solid
  unicode: f531
greater-than-equal:
  changes:
    - 5.0.13
  label: Greater Than Equal To
  search:
    terms: []
  styles:
    - solid
  unicode: f532
gripfire:
  changes:
    - 5.0.0
  label: 'Gripfire, Inc.'
  search:
    terms: []
  sponsored:
    label: 'Gripfire, Inc.'
    url: 'http://gripfire.io'
  styles:
    - brands
  unicode: f3ac
grunt:
  changes:
    - 5.0.0
  label: Grunt
  search:
    terms: []
  styles:
    - brands
  unicode: f3ad
gulp:
  changes:
    - 5.0.0
  label: Gulp
  search:
    terms: []
  styles:
    - brands
  unicode: f3ae
h-square:
  changes:
    - '3'
    - 5.0.0
  label: H Square
  search:
    terms:
      - hospital
      - hotel
  styles:
    - solid
  unicode: f0fd
hacker-news:
  changes:
    - '4.1'
    - 5.0.0
  label: Hacker News
  search:
    terms: []
  styles:
    - brands
  unicode: f1d4
hacker-news-square:
  changes:
    - 5.0.0
  label: Hacker News Square
  search:
    terms: []
  styles:
    - brands
  unicode: f3af
hand-holding:
  changes:
    - 5.0.9
  label: Hand Holding
  search:
    terms: []
  styles:
    - solid
  unicode: f4bd
hand-holding-heart:
  changes:
    - 5.0.9
  label: Hand Holding Heart
  search:
    terms: []
  styles:
    - solid
  unicode: f4be
hand-holding-usd:
  changes:
    - 5.0.9
  label: Hand Holding US Dollar
  search:
    terms:
      - dollar sign
      - price
      - donation
      - giving
  styles:
    - solid
  unicode: f4c0
hand-lizard:
  changes:
    - '4.4'
    - 5.0.0
  label: Lizard (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f258
hand-paper:
  changes:
    - '4.4'
    - 5.0.0
  label: Paper (Hand)
  search:
    terms:
      - stop
  styles:
    - solid
    - regular
  unicode: f256
hand-peace:
  changes:
    - '4.4'
    - 5.0.0
  label: Peace (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f25b
hand-point-down:
  changes:
    - '2'
    - 5.0.0
  label: Hand Pointing Down
  search:
    terms:
      - point
      - finger
      - hand-o-down
  styles:
    - solid
    - regular
  unicode: f0a7
hand-point-left:
  changes:
    - '2'
    - 5.0.0
  label: Hand Pointing Left
  search:
    terms:
      - point
      - left
      - previous
      - back
      - finger
      - hand-o-left
  styles:
    - solid
    - regular
  unicode: f0a5
hand-point-right:
  changes:
    - '2'
    - 5.0.0
  label: Hand Pointing Right
  search:
    terms:
      - point
      - right
      - next
      - forward
      - finger
      - hand-o-right
  styles:
    - solid
    - regular
  unicode: f0a4
hand-point-up:
  changes:
    - '2'
    - 5.0.0
  label: Hand Pointing Up
  search:
    terms:
      - point
      - finger
      - hand-o-up
  styles:
    - solid
    - regular
  unicode: f0a6
hand-pointer:
  changes:
    - '4.4'
    - 5.0.0
  label: Pointer (Hand)
  search:
    terms:
      - select
  styles:
    - solid
    - regular
  unicode: f25a
hand-rock:
  changes:
    - '4.4'
    - 5.0.0
  label: Rock (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f255
hand-scissors:
  changes:
    - '4.4'
    - 5.0.0
  label: Scissors (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f257
hand-spock:
  changes:
    - '4.4'
    - 5.0.0
  label: Spock (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f259
hands:
  changes:
    - 5.0.9
  label: Hands
  search:
    terms: []
  styles:
    - solid
  unicode: f4c2
hands-helping:
  changes:
    - 5.0.9
  label: Helping Hands
  search:
    terms:
      - assistance
      - aid
      - partnership
      - volunteering
  styles:
    - solid
  unicode: f4c4
handshake:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.9
  label: Handshake
  search:
    terms:
      - greeting
      - partnership
  styles:
    - solid
    - regular
  unicode: f2b5
hashtag:
  changes:
    - '4.5'
    - 5.0.0
  label: Hashtag
  search:
    terms: []
  styles:
    - solid
  unicode: f292
hdd:
  changes:
    - '2'
    - 5.0.0
  label: HDD
  search:
    terms:
      - harddrive
      - hard drive
      - storage
      - save
      - machine
  styles:
    - solid
    - regular
  unicode: f0a0
heading:
  changes:
    - '4.1'
    - 5.0.0
  label: heading
  search:
    terms:
      - header
      - header
  styles:
    - solid
  unicode: f1dc
headphones:
  changes:
    - '1'
    - 5.0.0
  label: headphones
  search:
    terms:
      - sound
      - listen
      - music
      - audio
      - speaker
  styles:
    - solid
  unicode: f025
heart:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: Heart
  search:
    terms:
      - love
      - like
      - favorite
  styles:
    - solid
    - regular
  unicode: f004
heartbeat:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.7
  label: Heartbeat
  search:
    terms:
      - ekg
      - vital signs
      - lifeline
  styles:
    - solid
  unicode: f21e
helicopter:
  changes:
    - 5.0.13
  label: Helicopter
  search:
    terms:
      - chopper
      - airwolf
      - apache
      - fly
      - flight
  sponsored:
    label: FLEETPLAN
    url: 'https://www.fleetplan.net'
  styles:
    - solid
  unicode: f533
hips:
  changes:
    - 5.0.5
  label: Hips
  search:
    terms: []
  styles:
    - brands
  unicode: f452
hire-a-helper:
  changes:
    - 5.0.0
  label: HireAHelper
  search:
    terms: []
  sponsored:
    label: HireAHelper
    url: 'https://www.hireahelper.com'
  styles:
    - brands
  unicode: f3b0
history:
  changes:
    - '4.1'
    - 5.0.0
  label: History
  search:
    terms: []
  styles:
    - solid
  unicode: f1da
hockey-puck:
  changes:
    - 5.0.5
  label: Hockey Puck
  search:
    terms: []
  styles:
    - solid
  unicode: f453
home:
  changes:
    - '1'
    - 5.0.0
  label: home
  search:
    terms:
      - main
      - house
  styles:
    - solid
  unicode: f015
hooli:
  changes:
    - 5.0.0
  label: Hooli
  search:
    terms: []
  styles:
    - brands
  unicode: f427
hospital:
  changes:
    - '3'
    - 5.0.0
  label: hospital
  search:
    terms:
      - building
      - medical center
      - emergency room
  styles:
    - solid
    - regular
  unicode: f0f8
hospital-alt:
  changes:
    - 5.0.7
  label: Alternate Hospital
  search:
    terms:
      - building
      - medical center
      - emergency room
  styles:
    - solid
  unicode: f47d
hospital-symbol:
  changes:
    - 5.0.7
  label: Hospital Symbol
  search:
    terms: []
  styles:
    - solid
  unicode: f47e
hotjar:
  changes:
    - 5.0.0
  label: Hotjar
  search:
    terms: []
  sponsored:
    label: Hotjar
    url: 'https://www.hotjar.com'
  styles:
    - brands
  unicode: f3b1
hourglass:
  changes:
    - '4.4'
    - 5.0.0
  label: Hourglass
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f254
hourglass-end:
  changes:
    - '4.4'
    - 5.0.0
  label: Hourglass End
  search:
    terms: []
  styles:
    - solid
  unicode: f253
hourglass-half:
  changes:
    - '4.4'
    - 5.0.0
  label: Hourglass Half
  search:
    terms: []
  styles:
    - solid
  unicode: f252
hourglass-start:
  changes:
    - '4.4'
    - 5.0.0
  label: Hourglass Start
  search:
    terms: []
  styles:
    - solid
  unicode: f251
houzz:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.9
  label: Houzz
  search:
    terms: []
  styles:
    - brands
  unicode: f27c
html5:
  changes:
    - '3.1'
    - 5.0.0
  label: HTML 5 Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f13b
hubspot:
  changes:
    - 5.0.0
  label: HubSpot
  search:
    terms: []
  sponsored:
    label: HubSpot
    url: 'http://www.HubSpot.com'
  styles:
    - brands
  unicode: f3b2
i-cursor:
  changes:
    - '4.4'
    - 5.0.0
  label: I Beam Cursor
  search:
    terms: []
  styles:
    - solid
  unicode: f246
id-badge:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
  label: Identification Badge
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2c1
id-card:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
  label: Identification Card
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2c2
id-card-alt:
  changes:
    - 5.0.7
  label: Alternate Identification Card
  search:
    terms:
      - demographics
  styles:
    - solid
  unicode: f47f
image:
  changes:
    - '1'
    - 5.0.0
  label: Image
  search:
    terms:
      - photo
      - album
      - picture
      - picture
  styles:
    - solid
    - regular
  unicode: f03e
images:
  changes:
    - '1'
    - 5.0.0
  label: Images
  search:
    terms:
      - photo
      - album
      - picture
  styles:
    - solid
    - regular
  unicode: f302
imdb:
  changes:
    - '4.7'
    - 5.0.0
  label: IMDB
  search:
    terms: []
  styles:
    - brands
  unicode: f2d8
inbox:
  changes:
    - '1'
    - 5.0.0
  label: inbox
  search:
    terms: []
  styles:
    - solid
  unicode: f01c
indent:
  changes:
    - '1'
    - 5.0.0
  label: Indent
  search:
    terms: []
  styles:
    - solid
  unicode: f03c
industry:
  changes:
    - '4.4'
    - 5.0.0
  label: Industry
  search:
    terms:
      - factory
      - manufacturing
  styles:
    - solid
  unicode: f275
infinity:
  changes:
    - 5.0.13
  label: Infinity
  search:
    terms: []
  styles:
    - solid
  unicode: f534
info:
  changes:
    - '3.1'
    - 5.0.0
  label: Info
  search:
    terms:
      - help
      - information
      - more
      - details
  styles:
    - solid
  unicode: f129
info-circle:
  changes:
    - '1'
    - 5.0.0
  label: Info Circle
  search:
    terms:
      - help
      - information
      - more
      - details
  styles:
    - solid
  unicode: f05a
instagram:
  changes:
    - '4.6'
    - 5.0.0
  label: Instagram
  search:
    terms: []
  styles:
    - brands
  unicode: f16d
internet-explorer:
  changes:
    - '4.4'
    - 5.0.0
  label: Internet-explorer
  search:
    terms:
      - browser
      - ie
  styles:
    - brands
  unicode: f26b
ioxhost:
  changes:
    - '4.2'
    - 5.0.0
  label: ioxhost
  search:
    terms: []
  styles:
    - brands
  unicode: f208
italic:
  changes:
    - '1'
    - 5.0.0
  label: italic
  search:
    terms:
      - italics
  styles:
    - solid
  unicode: f033
itunes:
  changes:
    - 5.0.0
  label: iTunes
  search:
    terms: []
  styles:
    - brands
  unicode: f3b4
itunes-note:
  changes:
    - 5.0.0
  label: Itunes Note
  search:
    terms: []
  styles:
    - brands
  unicode: f3b5
java:
  changes:
    - 5.0.10
  label: Java
  search:
    terms: []
  styles:
    - brands
  unicode: f4e4
jedi-order:
  changes:
    - 5.0.12
  label: Jedi Order
  search:
    terms: []
  styles:
    - brands
  unicode: f50e
jenkins:
  changes:
    - 5.0.0
  label: Jenkis
  search:
    terms: []
  styles:
    - brands
  unicode: f3b6
joget:
  changes:
    - 5.0.0
  label: Joget
  search:
    terms: []
  sponsored:
    label: Joget
    url: 'http://www.joget.org'
  styles:
    - brands
  unicode: f3b7
joomla:
  changes:
    - '4.1'
    - 5.0.0
  label: Joomla Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1aa
js:
  changes:
    - 5.0.0
  label: JavaScript (JS)
  search:
    terms: []
  styles:
    - brands
  unicode: f3b8
js-square:
  changes:
    - 5.0.0
    - 5.0.3
  label: JavaScript (JS) Square
  search:
    terms: []
  styles:
    - brands
  unicode: f3b9
jsfiddle:
  changes:
    - '4.1'
    - 5.0.0
  label: jsFiddle
  search:
    terms: []
  styles:
    - brands
  unicode: f1cc
key:
  changes:
    - '1'
    - 5.0.0
  label: key
  search:
    terms:
      - unlock
      - password
  styles:
    - solid
  unicode: f084
keybase:
  changes:
    - 5.0.11
  label: Keybase
  search:
    terms: []
  styles:
    - brands
  unicode: f4f5
keyboard:
  changes:
    - '3.1'
    - 5.0.0
  label: Keyboard
  search:
    terms:
      - type
      - input
  styles:
    - solid
    - regular
  unicode: f11c
keycdn:
  changes:
    - 5.0.0
  label: KeyCDN
  search:
    terms: []
  sponsored:
    label: KeyCDN
    url: 'https://www.keycdn.com'
  styles:
    - brands
  unicode: f3ba
kickstarter:
  changes:
    - 5.0.0
  label: Kickstarter
  search:
    terms: []
  styles:
    - brands
  unicode: f3bb
kickstarter-k:
  changes:
    - 5.0.0
  label: Kickstarter K
  search:
    terms: []
  styles:
    - brands
  unicode: f3bc
kiwi-bird:
  changes:
    - 5.0.13
  label: Kiwi Bird
  search:
    terms: []
  sponsored:
    label: Purely Interactive
    url: 'https://www.purelyinteractive.ca'
  styles:
    - solid
  unicode: f535
korvue:
  changes:
    - 5.0.2
  label: KORVUE
  search:
    terms: []
  sponsored:
    label: Korvue
    url: 'https://korvue.com'
  styles:
    - brands
  unicode: f42f
language:
  changes:
    - '4.1'
    - 5.0.0
  label: Language
  search:
    terms: []
  styles:
    - solid
  unicode: f1ab
laptop:
  changes:
    - '3'
    - 5.0.0
  label: Laptop
  search:
    terms:
      - demo
      - computer
      - device
      - pc
      - mac
      - pc
      - macbook
      - dell
      - dude you're getting
      - machine
  styles:
    - solid
  unicode: f109
laravel:
  changes:
    - 5.0.0
    - 5.0.3
  label: Laravel
  search:
    terms: []
  styles:
    - brands
  unicode: f3bd
lastfm:
  changes:
    - '4.2'
    - 5.0.0
  label: last.fm
  search:
    terms: []
  styles:
    - brands
  unicode: f202
lastfm-square:
  changes:
    - '4.2'
    - 5.0.0
    - 5.0.11
  label: last.fm Square
  search:
    terms: []
  styles:
    - brands
  unicode: f203
leaf:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: leaf
  search:
    terms:
      - eco
      - nature
      - plant
  styles:
    - solid
  unicode: f06c
leanpub:
  changes:
    - '4.3'
    - 5.0.0
  label: Leanpub
  search:
    terms: []
  styles:
    - brands
  unicode: f212
lemon:
  changes:
    - '1'
    - 5.0.0
  label: Lemon
  search:
    terms:
      - food
  styles:
    - solid
    - regular
  unicode: f094
less:
  changes:
    - 5.0.0
  label: Less
  search:
    terms: []
  styles:
    - brands
  unicode: f41d
less-than:
  changes:
    - 5.0.13
  label: Less Than
  search:
    terms: []
  styles:
    - solid
  unicode: f536
less-than-equal:
  changes:
    - 5.0.13
  label: Less Than Equal To
  search:
    terms: []
  styles:
    - solid
  unicode: f537
level-down-alt:
  changes:
    - 5.0.0
  label: Alternate Level Down
  search:
    terms:
      - level-down
  styles:
    - solid
  unicode: f3be
level-up-alt:
  changes:
    - 5.0.0
  label: Alternate Level Up
  search:
    terms:
      - level-up
  styles:
    - solid
  unicode: f3bf
life-ring:
  changes:
    - '4.1'
    - 5.0.0
  label: Life Ring
  search:
    terms:
      - support
  styles:
    - solid
    - regular
  unicode: f1cd
lightbulb:
  changes:
    - '3'
    - 5.0.0
  label: Lightbulb
  search:
    terms:
      - idea
      - inspiration
  styles:
    - solid
    - regular
  unicode: f0eb
line:
  changes:
    - 5.0.0
  label: Line
  search:
    terms: []
  styles:
    - brands
  unicode: f3c0
link:
  changes:
    - '2'
    - 5.0.0
  label: Link
  search:
    terms:
      - chain
  styles:
    - solid
  unicode: f0c1
linkedin:
  changes:
    - '1'
    - 5.0.0
  label: LinkedIn
  search:
    terms:
      - linkedin-square
  styles:
    - brands
  unicode: f08c
linkedin-in:
  changes:
    - '2'
    - 5.0.0
  label: LinkedIn In
  search:
    terms:
      - linkedin
  styles:
    - brands
  unicode: f0e1
linode:
  changes:
    - '4.7'
    - 5.0.0
  label: Linode
  search:
    terms: []
  styles:
    - brands
  unicode: f2b8
linux:
  changes:
    - '3.2'
    - 5.0.0
  label: Linux
  search:
    terms:
      - tux
  styles:
    - brands
  unicode: f17c
lira-sign:
  changes:
    - '4'
    - 5.0.0
  label: Turkish Lira Sign
  search:
    terms:
      - try
      - turkish
      - try
  styles:
    - solid
  unicode: f195
list:
  changes:
    - '1'
    - 5.0.0
  label: List
  search:
    terms:
      - ul
      - ol
      - checklist
      - finished
      - completed
      - done
      - todo
  styles:
    - solid
  unicode: f03a
list-alt:
  changes:
    - '1'
    - 5.0.0
  label: Alternate List
  search:
    terms:
      - ul
      - ol
      - checklist
      - finished
      - completed
      - done
      - todo
  styles:
    - solid
    - regular
  unicode: f022
list-ol:
  changes:
    - '2'
    - 5.0.0
  label: list-ol
  search:
    terms:
      - ul
      - ol
      - checklist
      - list
      - todo
      - list
      - numbers
  styles:
    - solid
  unicode: f0cb
list-ul:
  changes:
    - '2'
    - 5.0.0
  label: list-ul
  search:
    terms:
      - ul
      - ol
      - checklist
      - todo
      - list
  styles:
    - solid
  unicode: f0ca
location-arrow:
  changes:
    - '3.1'
    - 5.0.0
  label: location-arrow
  search:
    terms:
      - map
      - coordinates
      - location
      - address
      - place
      - where
      - gps
  styles:
    - solid
  unicode: f124
lock:
  changes:
    - '1'
    - 5.0.0
  label: lock
  search:
    terms:
      - protect
      - admin
      - security
  styles:
    - solid
  unicode: f023
lock-open:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.1
  label: Lock Open
  search:
    terms:
      - protect
      - admin
      - password
      - lock
      - open
  styles:
    - solid
  unicode: f3c1
long-arrow-alt-down:
  changes:
    - 5.0.0
  label: Alternate Long Arrow Down
  search:
    terms:
      - long-arrow-down
  styles:
    - solid
  unicode: f309
long-arrow-alt-left:
  changes:
    - 5.0.0
  label: Alternate Long Arrow Left
  search:
    terms:
      - previous
      - back
      - long-arrow-left
  styles:
    - solid
  unicode: f30a
long-arrow-alt-right:
  changes:
    - 5.0.0
  label: Alternate Long Arrow Right
  search:
    terms:
      - long-arrow-right
  styles:
    - solid
  unicode: f30b
long-arrow-alt-up:
  changes:
    - 5.0.0
  label: Alternate Long Arrow Up
  search:
    terms:
      - long-arrow-up
  styles:
    - solid
  unicode: f30c
low-vision:
  changes:
    - '4.6'
    - 5.0.0
  label: Low Vision
  search:
    terms: []
  styles:
    - solid
  unicode: f2a8
lyft:
  changes:
    - 5.0.0
  label: lyft
  search:
    terms: []
  styles:
    - brands
  unicode: f3c3
magento:
  changes:
    - 5.0.0
  label: Magento
  search:
    terms: []
  styles:
    - brands
  unicode: f3c4
magic:
  changes:
    - '2'
    - 5.0.0
  label: magic
  search:
    terms:
      - wizard
      - automatic
      - autocomplete
  styles:
    - solid
  unicode: f0d0
magnet:
  changes:
    - '1'
    - 5.0.0
  label: magnet
  search:
    terms: []
  styles:
    - solid
  unicode: f076
male:
  changes:
    - '3.2'
    - 5.0.0
  label: Male
  search:
    terms:
      - man
      - human
      - user
      - person
      - profile
  styles:
    - solid
  unicode: f183
mandalorian:
  changes:
    - 5.0.12
  label: Mandalorian
  search:
    terms: []
  styles:
    - brands
  unicode: f50f
map:
  changes:
    - '4.4'
    - 5.0.0
  label: Map
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f279
map-marker:
  changes:
    - '1'
    - 5.0.0
  label: map-marker
  search:
    terms:
      - map
      - pin
      - location
      - coordinates
      - localize
      - address
      - travel
      - where
      - place
      - gps
  styles:
    - solid
  unicode: f041
map-marker-alt:
  changes:
    - 5.0.0
  label: Alternate Map Marker
  search:
    terms:
      - map-marker
      - gps
  styles:
    - solid
  unicode: f3c5
map-pin:
  changes:
    - '4.4'
    - 5.0.0
  label: Map Pin
  search:
    terms: []
  styles:
    - solid
  unicode: f276
map-signs:
  changes:
    - '4.4'
    - 5.0.0
  label: Map Signs
  search:
    terms: []
  styles:
    - solid
  unicode: f277
mars:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars
  search:
    terms:
      - male
  styles:
    - solid
  unicode: f222
mars-double:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars Double
  search:
    terms: []
  styles:
    - solid
  unicode: f227
mars-stroke:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars Stroke
  search:
    terms: []
  styles:
    - solid
  unicode: f229
mars-stroke-h:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars Stroke Horizontal
  search:
    terms: []
  styles:
    - solid
  unicode: f22b
mars-stroke-v:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars Stroke Vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f22a
mastodon:
  changes:
    - 5.0.11
  label: Mastodon
  search:
    terms: []
  styles:
    - brands
  unicode: f4f6
maxcdn:
  changes:
    - '3.1'
    - 5.0.0
  label: MaxCDN
  search:
    terms: []
  styles:
    - brands
  unicode: f136
medapps:
  changes:
    - 5.0.0
  label: MedApps
  search:
    terms: []
  sponsored:
    label: MedApps
    url: 'http://medapps.com.au'
  styles:
    - brands
  unicode: f3c6
medium:
  changes:
    - '4.3'
    - 5.0.0
  label: Medium
  search:
    terms: []
  styles:
    - brands
  unicode: f23a
medium-m:
  changes:
    - 5.0.0
  label: Medium M
  search:
    terms: []
  styles:
    - brands
  unicode: f3c7
medkit:
  changes:
    - '3'
    - 5.0.0
  label: medkit
  search:
    terms:
      - first aid
      - firstaid
      - help
      - support
      - health
  styles:
    - solid
  unicode: f0fa
medrt:
  changes:
    - 5.0.0
  label: MRT
  search:
    terms: []
  sponsored:
    label: MRT
    url: 'https://medrt.co.jp'
  styles:
    - brands
  unicode: f3c8
meetup:
  changes:
    - '4.7'
    - 5.0.0
  label: Meetup
  search:
    terms: []
  styles:
    - brands
  unicode: f2e0
meh:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.9
  label: Meh
  search:
    terms:
      - face
      - emoticon
      - rating
      - neutral
  styles:
    - solid
    - regular
  unicode: f11a
memory:
  changes:
    - 5.0.13
  label: Memory
  search:
    terms:
      - RAM
      - DIMM
  styles:
    - solid
  unicode: f538
mercury:
  changes:
    - '4.3'
    - 5.0.0
  label: Mercury
  search:
    terms:
      - transgender
  styles:
    - solid
  unicode: f223
microchip:
  changes:
    - '4.7'
    - 5.0.0
  label: Microchip
  search:
    terms: []
  styles:
    - solid
  unicode: f2db
microphone:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.13
  label: microphone
  search:
    terms:
      - record
      - voice
      - sound
  styles:
    - solid
  unicode: f130
microphone-alt:
  changes:
    - 5.0.0
    - 5.0.13
  label: Alternate Microphone
  search:
    terms:
      - record
      - voice
      - sound
  styles:
    - solid
  unicode: f3c9
microphone-alt-slash:
  changes:
    - 5.0.13
  label: Alternate Microphone Slash
  search:
    terms:
      - record
      - voice
      - sound
      - mute
      - disable
  styles:
    - solid
  unicode: f539
microphone-slash:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.13
  label: Microphone Slash
  search:
    terms:
      - record
      - voice
      - sound
      - mute
      - disable
  styles:
    - solid
  unicode: f131
microsoft:
  changes:
    - 5.0.0
  label: Microsoft
  search:
    terms: []
  styles:
    - brands
  unicode: f3ca
minus:
  changes:
    - '1'
    - 5.0.0
  label: minus
  search:
    terms:
      - hide
      - minify
      - delete
      - remove
      - trash
      - hide
      - collapse
  styles:
    - solid
  unicode: f068
minus-circle:
  changes:
    - '1'
    - 5.0.0
  label: Minus Circle
  search:
    terms:
      - delete
      - remove
      - trash
      - hide
  styles:
    - solid
  unicode: f056
minus-square:
  changes:
    - '3.1'
    - 5.0.0
  label: Minus Square
  search:
    terms:
      - hide
      - minify
      - delete
      - remove
      - trash
      - hide
      - collapse
  styles:
    - solid
    - regular
  unicode: f146
mix:
  changes:
    - 5.0.0
    - 5.0.3
  label: Mix
  search:
    terms: []
  sponsored:
    label: Mix
    url: 'http://mix.com'
  styles:
    - brands
  unicode: f3cb
mixcloud:
  changes:
    - '4.5'
    - 5.0.0
  label: Mixcloud
  search:
    terms: []
  styles:
    - brands
  unicode: f289
mizuni:
  changes:
    - 5.0.0
  label: Mizuni
  search:
    terms: []
  sponsored:
    label: Mizuni
    url: 'http://www.mizuni.com'
  styles:
    - brands
  unicode: f3cc
mobile:
  changes:
    - '3'
    - 5.0.0
  label: Mobile Phone
  search:
    terms:
      - cell phone
      - cellphone
      - text
      - call
      - number
      - telephone
      - device
      - screen
      - apple
      - iphone
  styles:
    - solid
  unicode: f10b
mobile-alt:
  changes:
    - 5.0.0
  label: Alternate Mobile
  search:
    terms:
      - cell phone
      - cellphone
      - text
      - call
      - number
      - telephone
      - device
      - screen
      - apple
      - iphone
  styles:
    - solid
  unicode: f3cd
modx:
  changes:
    - '4.5'
    - 5.0.0
  label: MODX
  search:
    terms: []
  styles:
    - brands
  unicode: f285
monero:
  changes:
    - 5.0.0
  label: Monero
  search:
    terms: []
  styles:
    - brands
  unicode: f3d0
money-bill:
  changes:
    - '2'
    - 5.0.0
    - 5.0.13
  label: Money Bill
  search:
    terms:
      - cash
      - money
      - buy
      - checkout
      - purchase
      - payment
      - price
  styles:
    - solid
  unicode: f0d6
money-bill-alt:
  changes:
    - 5.0.0
    - 5.0.13
  label: Alternate Money Bill
  search:
    terms:
      - cash
      - money
      - buy
      - checkout
      - purchase
      - payment
      - price
  styles:
    - solid
    - regular
  unicode: f3d1
money-bill-wave:
  changes:
    - 5.0.13
  label: Wavy Money Bill
  search:
    terms: []
  styles:
    - solid
  unicode: f53a
money-bill-wave-alt:
  changes:
    - 5.0.13
  label: Alternate Wavy Money Bill
  search:
    terms: []
  styles:
    - solid
  unicode: f53b
money-check:
  changes:
    - 5.0.13
  label: Money Check
  search:
    terms:
      - cheque
      - bank check
  styles:
    - solid
  unicode: f53c
money-check-alt:
  changes:
    - 5.0.13
  label: Alternate Money Check
  search:
    terms:
      - cheque
      - bank check
  styles:
    - solid
  unicode: f53d
moon:
  changes:
    - '3.2'
    - 5.0.0
  label: Moon
  search:
    terms:
      - night
      - darker
      - contrast
  styles:
    - solid
    - regular
  unicode: f186
motorcycle:
  changes:
    - '4.3'
    - 5.0.0
  label: Motorcycle
  search:
    terms:
      - vehicle
      - machine
      - transportation
      - bike
  styles:
    - solid
  unicode: f21c
mouse-pointer:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.3
  label: Mouse Pointer
  search:
    terms:
      - select
  styles:
    - solid
  unicode: f245
music:
  changes:
    - '1'
    - 5.0.0
  label: Music
  search:
    terms:
      - note
      - sound
  styles:
    - solid
  unicode: f001
napster:
  changes:
    - 5.0.0
  label: Napster
  search:
    terms: []
  sponsored:
    label: Napster
    url: 'http://www.napster.com'
  styles:
    - brands
  unicode: f3d2
neuter:
  changes:
    - '4.3'
    - 5.0.0
  label: Neuter
  search:
    terms: []
  styles:
    - solid
  unicode: f22c
newspaper:
  changes:
    - '4.2'
    - 5.0.0
  label: Newspaper
  search:
    terms:
      - press
      - article
  styles:
    - solid
    - regular
  unicode: f1ea
nintendo-switch:
  changes:
    - 5.0.0
  label: Nintendo Switch
  search:
    terms: []
  styles:
    - brands
  unicode: f418
node:
  changes:
    - 5.0.0
  label: Node.js
  search:
    terms: []
  styles:
    - brands
  unicode: f419
node-js:
  changes:
    - 5.0.0
    - 5.0.3
  label: Node.js JS
  search:
    terms: []
  styles:
    - brands
  unicode: f3d3
not-equal:
  changes:
    - 5.0.13
  label: Not Equal
  search:
    terms: []
  styles:
    - solid
  unicode: f53e
notes-medical:
  changes:
    - 5.0.7
  label: Medical Notes
  search:
    terms: []
  styles:
    - solid
  unicode: f481
npm:
  changes:
    - 5.0.0
  label: npm
  search:
    terms: []
  styles:
    - brands
  unicode: f3d4
ns8:
  changes:
    - 5.0.0
  label: NS8
  search:
    terms: []
  sponsored:
    label: NS8
    url: 'https://www.ns8.com'
  styles:
    - brands
  unicode: f3d5
nutritionix:
  changes:
    - 5.0.0
  label: Nutritionix
  search:
    terms: []
  sponsored:
    label: Nutritionix
    url: 'http://www.nutritionix.com'
  styles:
    - brands
  unicode: f3d6
object-group:
  changes:
    - '4.4'
    - 5.0.0
  label: Object Group
  search:
    terms:
      - design
  styles:
    - solid
    - regular
  unicode: f247
object-ungroup:
  changes:
    - '4.4'
    - 5.0.0
  label: Object Ungroup
  search:
    terms:
      - design
  styles:
    - solid
    - regular
  unicode: f248
odnoklassniki:
  changes:
    - '4.4'
    - 5.0.0
  label: Odnoklassniki
  search:
    terms: []
  styles:
    - brands
  unicode: f263
odnoklassniki-square:
  changes:
    - '4.4'
    - 5.0.0
  label: Odnoklassniki Square
  search:
    terms: []
  styles:
    - brands
  unicode: f264
old-republic:
  changes:
    - 5.0.12
  label: Old Republic
  search:
    terms: []
  styles:
    - brands
  unicode: f510
opencart:
  changes:
    - '4.4'
    - 5.0.0
  label: OpenCart
  search:
    terms: []
  styles:
    - brands
  unicode: f23d
openid:
  changes:
    - '4.1'
    - 5.0.0
  label: OpenID
  search:
    terms: []
  styles:
    - brands
  unicode: f19b
opera:
  changes:
    - '4.4'
    - 5.0.0
  label: Opera
  search:
    terms: []
  styles:
    - brands
  unicode: f26a
optin-monster:
  changes:
    - '4.4'
    - 5.0.0
  label: Optin Monster
  search:
    terms: []
  styles:
    - brands
  unicode: f23c
osi:
  changes:
    - 5.0.0
  label: Open Source Initiative
  search:
    terms: []
  styles:
    - brands
  unicode: f41a
outdent:
  changes:
    - '1'
    - 5.0.0
  label: Outdent
  search:
    terms: []
  styles:
    - solid
  unicode: f03b
page4:
  changes:
    - 5.0.0
  label: page4 Corporation
  search:
    terms: []
  sponsored:
    label: page4 Corporation
    url: 'https://en.page4.com'
  styles:
    - brands
  unicode: f3d7
pagelines:
  changes:
    - '4'
    - 5.0.0
  label: Pagelines
  search:
    terms:
      - leaf
      - leaves
      - tree
      - plant
      - eco
      - nature
  styles:
    - brands
  unicode: f18c
paint-brush:
  changes:
    - '4.2'
    - 5.0.0
  label: Paint Brush
  search:
    terms: []
  styles:
    - solid
  unicode: f1fc
palette:
  changes:
    - 5.0.13
  label: Palette
  search:
    terms: []
  styles:
    - solid
  unicode: f53f
palfed:
  changes:
    - 5.0.0
    - 5.0.3
  label: Palfed
  search:
    terms: []
  sponsored:
    label: PalFed
    url: 'https://www.palfed.com'
  styles:
    - brands
  unicode: f3d8
pallet:
  changes:
    - 5.0.7
  label: Pallet
  search:
    terms: []
  styles:
    - solid
  unicode: f482
paper-plane:
  changes:
    - '4.1'
    - 5.0.0
  label: Paper Plane
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1d8
paperclip:
  changes:
    - '2'
    - 5.0.0
  label: Paperclip
  search:
    terms:
      - attachment
  styles:
    - solid
  unicode: f0c6
parachute-box:
  changes:
    - 5.0.9
  label: Parachute Box
  search:
    terms:
      - aid
      - assistance
      - rescue
      - supplies
  styles:
    - solid
  unicode: f4cd
paragraph:
  changes:
    - '4.1'
    - 5.0.0
  label: paragraph
  search:
    terms: []
  styles:
    - solid
  unicode: f1dd
parking:
  changes:
    - 5.0.13
  label: Parking
  search:
    terms: []
  styles:
    - solid
  unicode: f540
paste:
  changes:
    - '2'
    - 5.0.0
  label: Paste
  search:
    terms:
      - copy
      - clipboard
  styles:
    - solid
  unicode: f0ea
patreon:
  changes:
    - 5.0.0
    - 5.0.3
  label: Patreon
  search:
    terms: []
  styles:
    - brands
  unicode: f3d9
pause:
  changes:
    - '1'
    - 5.0.0
  label: pause
  search:
    terms:
      - wait
  styles:
    - solid
  unicode: f04c
pause-circle:
  changes:
    - '4.5'
    - 5.0.0
  label: Pause Circle
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f28b
paw:
  changes:
    - '4.1'
    - 5.0.0
  label: Paw
  search:
    terms:
      - pet
  styles:
    - solid
  unicode: f1b0
paypal:
  changes:
    - '4.2'
    - 5.0.0
  label: Paypal
  search:
    terms: []
  styles:
    - brands
  unicode: f1ed
pen-square:
  changes:
    - '3.1'
    - 5.0.0
  label: Pen Square
  search:
    terms:
      - write
      - edit
      - update
      - pencil-square
  styles:
    - solid
  unicode: f14b
pencil-alt:
  changes:
    - 5.0.0
  label: Alternate Pencil
  search:
    terms:
      - write
      - edit
      - update
      - pencil
      - design
  styles:
    - solid
  unicode: f303
people-carry:
  changes:
    - 5.0.9
  label: People Carry
  search:
    terms:
      - movers
  styles:
    - solid
  unicode: f4ce
percent:
  changes:
    - '4.5'
    - 5.0.0
  label: Percent
  search:
    terms: []
  styles:
    - solid
  unicode: f295
percentage:
  changes:
    - 5.0.13
  label: Percentage
  search:
    terms: []
  styles:
    - solid
  unicode: f541
periscope:
  changes:
    - 5.0.0
  label: Periscope
  search:
    terms: []
  styles:
    - brands
  unicode: f3da
phabricator:
  changes:
    - 5.0.0
  label: Phabricator
  search:
    terms: []
  sponsored:
    label: Phabricator
    url: 'http://phacility.com'
  styles:
    - brands
  unicode: f3db
phoenix-framework:
  changes:
    - 5.0.0
    - 5.0.3
  label: Phoenix Framework
  search:
    terms: []
  styles:
    - brands
  unicode: f3dc
phoenix-squadron:
  changes:
    - 5.0.12
  label: Phoenix Squadron
  search:
    terms: []
  styles:
    - brands
  unicode: f511
phone:
  changes:
    - '2'
    - 5.0.0
  label: Phone
  search:
    terms:
      - call
      - voice
      - number
      - support
      - earphone
      - telephone
  styles:
    - solid
  unicode: f095
phone-slash:
  changes:
    - 5.0.0
    - 5.0.9
  label: Phone Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f3dd
phone-square:
  changes:
    - '2'
    - 5.0.0
  label: Phone Square
  search:
    terms:
      - call
      - voice
      - number
      - support
      - telephone
  styles:
    - solid
  unicode: f098
phone-volume:
  changes:
    - '4.6'
    - 5.0.0
    - 5.0.3
  label: Phone Volume
  search:
    terms:
      - telephone
      - volume-control-phone
  styles:
    - solid
  unicode: f2a0
php:
  changes:
    - 5.0.5
  label: PHP
  search:
    terms: []
  styles:
    - brands
  unicode: f457
pied-piper:
  changes:
    - '4.6'
    - 5.0.0
    - 5.0.10
  label: Pied Piper Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f2ae
pied-piper-alt:
  changes:
    - '4.1'
    - 5.0.0
  label: Alternate Pied Piper Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a8
pied-piper-hat:
  changes:
    - 5.0.10
  label: Pied Piper-hat
  search:
    terms: []
  styles:
    - brands
  unicode: f4e5
pied-piper-pp:
  changes:
    - '4.1'
    - 5.0.0
  label: Pied Piper PP Logo (Old)
  search:
    terms: []
  styles:
    - brands
  unicode: f1a7
piggy-bank:
  changes:
    - 5.0.9
  label: Piggy Bank
  search:
    terms:
      - savings
      - save
  styles:
    - solid
  unicode: f4d3
pills:
  changes:
    - 5.0.7
  label: Pills
  search:
    terms:
      - medicine
      - drugs
  styles:
    - solid
  unicode: f484
pinterest:
  changes:
    - '2'
    - 5.0.0
  label: Pinterest
  search:
    terms: []
  styles:
    - brands
  unicode: f0d2
pinterest-p:
  changes:
    - '4.3'
    - 5.0.0
  label: Pinterest P
  search:
    terms: []
  styles:
    - brands
  unicode: f231
pinterest-square:
  changes:
    - '2'
    - 5.0.0
  label: Pinterest Square
  search:
    terms: []
  styles:
    - brands
  unicode: f0d3
plane:
  changes:
    - '1'
    - 5.0.0
    - 5.0.13
  label: plane
  search:
    terms:
      - travel
      - trip
      - location
      - destination
      - airplane
      - fly
      - mode
  styles:
    - solid
  unicode: f072
play:
  changes:
    - '1'
    - 5.0.0
  label: play
  search:
    terms:
      - start
      - playing
      - music
      - sound
  styles:
    - solid
  unicode: f04b
play-circle:
  changes:
    - '3.1'
    - 5.0.0
  label: Play Circle
  search:
    terms:
      - start
      - playing
  styles:
    - solid
    - regular
  unicode: f144
playstation:
  changes:
    - 5.0.0
  label: PlayStation
  search:
    terms: []
  styles:
    - brands
  unicode: f3df
plug:
  changes:
    - '4.2'
    - 5.0.0
  label: Plug
  search:
    terms:
      - power
      - connect
      - online
  styles:
    - solid
  unicode: f1e6
plus:
  changes:
    - '1'
    - 5.0.0
    - 5.0.13
  label: plus
  search:
    terms:
      - add
      - new
      - create
      - expand
  styles:
    - solid
  unicode: f067
plus-circle:
  changes:
    - '1'
    - 5.0.0
  label: Plus Circle
  search:
    terms:
      - add
      - new
      - create
      - expand
  styles:
    - solid
  unicode: f055
plus-square:
  changes:
    - '3'
    - 5.0.0
  label: Plus Square
  search:
    terms:
      - add
      - new
      - create
      - expand
  styles:
    - solid
    - regular
  unicode: f0fe
podcast:
  changes:
    - '4.7'
    - 5.0.0
  label: Podcast
  search:
    terms: []
  styles:
    - solid
  unicode: f2ce
poo:
  changes:
    - 5.0.0
    - 5.0.9
  label: Poo
  search:
    terms: []
  styles:
    - solid
  unicode: f2fe
portrait:
  changes:
    - 5.0.0
    - 5.0.3
  label: Portrait
  search:
    terms: []
  styles:
    - solid
  unicode: f3e0
pound-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Pound Sign
  search:
    terms:
      - gbp
      - gbp
  styles:
    - solid
  unicode: f154
power-off:
  changes:
    - '1'
    - 5.0.0
  label: Power Off
  search:
    terms:
      - 'on'
  styles:
    - solid
  unicode: f011
prescription-bottle:
  changes:
    - 5.0.7
  label: Prescription Bottle
  search:
    terms:
      - prescription
      - rx
  styles:
    - solid
  unicode: f485
prescription-bottle-alt:
  changes:
    - 5.0.7
  label: Alternate Prescription Bottle
  search:
    terms:
      - prescription
      - rx
  styles:
    - solid
  unicode: f486
print:
  changes:
    - '1'
    - 5.0.0
  label: print
  search:
    terms: []
  styles:
    - solid
  unicode: f02f
procedures:
  changes:
    - 5.0.7
  label: Procedures
  search:
    terms: []
  styles:
    - solid
  unicode: f487
product-hunt:
  changes:
    - '4.5'
    - 5.0.0
  label: Product Hunt
  search:
    terms: []
  styles:
    - brands
  unicode: f288
project-diagram:
  changes:
    - 5.0.13
  label: Project Diagram
  search:
    terms: []
  sponsored:
    label: Silicon Barn Inc
    url: 'https://siliconbarn.com'
  styles:
    - solid
  unicode: f542
pushed:
  changes:
    - 5.0.0
  label: Pushed
  search:
    terms: []
  sponsored:
    label: Pushed
    url: 'https://pushed.co'
  styles:
    - brands
  unicode: f3e1
puzzle-piece:
  changes:
    - '3.1'
    - 5.0.0
  label: Puzzle Piece
  search:
    terms:
      - addon
      - add-on
      - section
  styles:
    - solid
  unicode: f12e
python:
  changes:
    - 5.0.0
  label: Python
  search:
    terms: []
  styles:
    - brands
  unicode: f3e2
qq:
  changes:
    - '4.1'
    - 5.0.0
  label: QQ
  search:
    terms: []
  styles:
    - brands
  unicode: f1d6
qrcode:
  changes:
    - '1'
    - 5.0.0
  label: qrcode
  search:
    terms:
      - scan
  styles:
    - solid
  unicode: f029
question:
  changes:
    - '3.1'
    - 5.0.0
  label: Question
  search:
    terms:
      - help
      - information
      - unknown
      - support
  styles:
    - solid
  unicode: f128
question-circle:
  changes:
    - '1'
    - 5.0.0
  label: Question Circle
  search:
    terms:
      - help
      - information
      - unknown
      - support
  styles:
    - solid
    - regular
  unicode: f059
quidditch:
  changes:
    - 5.0.5
  label: Quidditch
  search:
    terms: []
  styles:
    - solid
  unicode: f458
quinscape:
  changes:
    - 5.0.5
  label: QuinScape
  search:
    terms: []
  sponsored:
    label: QuinScape
    url: 'https://www.quinscape.de'
  styles:
    - brands
  unicode: f459
quora:
  changes:
    - '4.7'
    - 5.0.0
  label: Quora
  search:
    terms: []
  styles:
    - brands
  unicode: f2c4
quote-left:
  changes:
    - '3'
    - 5.0.0
    - 5.0.9
  label: quote-left
  search:
    terms: []
  styles:
    - solid
  unicode: f10d
quote-right:
  changes:
    - '3'
    - 5.0.0
    - 5.0.9
  label: quote-right
  search:
    terms: []
  styles:
    - solid
  unicode: f10e
r-project:
  changes:
    - 5.0.11
    - 5.0.12
  label: R Project
  search:
    terms: []
  styles:
    - brands
  unicode: f4f7
random:
  changes:
    - '1'
    - 5.0.0
  label: random
  search:
    terms:
      - sort
      - shuffle
  styles:
    - solid
  unicode: f074
ravelry:
  changes:
    - '4.7'
    - 5.0.0
  label: Ravelry
  search:
    terms: []
  styles:
    - brands
  unicode: f2d9
react:
  changes:
    - 5.0.0
  label: React
  search:
    terms: []
  styles:
    - brands
  unicode: f41b
readme:
  changes:
    - 5.0.9
    - 5.0.10
  label: ReadMe
  search:
    terms: []
  sponsored:
    label: Readme.io
    url: 'http://readme.io'
  styles:
    - brands
  unicode: f4d5
rebel:
  changes:
    - '4.1'
    - 5.0.0
  label: Rebel Alliance
  search:
    terms: []
  styles:
    - brands
  unicode: f1d0
receipt:
  changes:
    - 5.0.13
  label: Receipt
  search:
    terms:
      - table
      - check
      - invoice
  styles:
    - solid
  unicode: f543
recycle:
  changes:
    - '4.1'
    - 5.0.0
  label: Recycle
  search:
    terms: []
  styles:
    - solid
  unicode: f1b8
red-river:
  changes:
    - 5.0.0
  label: red river
  search:
    terms: []
  sponsored:
    label: red river
    url: 'https://river.red'
  styles:
    - brands
  unicode: f3e3
reddit:
  changes:
    - '4.1'
    - 5.0.0
  label: reddit Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a1
reddit-alien:
  changes:
    - '4.5'
    - 5.0.0
  label: reddit Alien
  search:
    terms: []
  styles:
    - brands
  unicode: f281
reddit-square:
  changes:
    - '4.1'
    - 5.0.0
  label: reddit Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1a2
redo:
  changes:
    - '1'
    - 5.0.0
  label: Redo
  search:
    terms:
      - forward
      - repeat
      - repeat
  styles:
    - solid
  unicode: f01e
redo-alt:
  changes:
    - 5.0.0
  label: Alternate Redo
  search:
    terms:
      - forward
      - repeat
  styles:
    - solid
  unicode: f2f9
registered:
  changes:
    - '4.4'
    - 5.0.0
  label: Registered Trademark
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f25d
rendact:
  changes:
    - 5.0.0
  label: Rendact
  search:
    terms: []
  sponsored:
    label: Rendact
    url: 'https://rendact.com'
  styles:
    - brands
  unicode: f3e4
renren:
  changes:
    - '3.2'
    - 5.0.0
  label: Renren
  search:
    terms: []
  styles:
    - brands
  unicode: f18b
reply:
  changes:
    - '3'
    - 5.0.0
  label: Reply
  search:
    terms: []
  styles:
    - solid
  unicode: f3e5
reply-all:
  changes:
    - '3.1'
    - 5.0.0
  label: reply-all
  search:
    terms: []
  styles:
    - solid
  unicode: f122
replyd:
  changes:
    - 5.0.0
  label: replyd
  search:
    terms: []
  sponsored:
    label: replyd
  styles:
    - brands
  unicode: f3e6
researchgate:
  changes:
    - 5.0.11
  label: Researchgate
  search:
    terms: []
  styles:
    - brands
  unicode: f4f8
resolving:
  changes:
    - 5.0.0
  label: Resolving
  search:
    terms: []
  sponsored:
    label: Resolving
    url: 'https://resolving.com'
  styles:
    - brands
  unicode: f3e7
retweet:
  changes:
    - '1'
    - 5.0.0
  label: Retweet
  search:
    terms:
      - refresh
      - reload
      - share
      - swap
  styles:
    - solid
  unicode: f079
ribbon:
  changes:
    - 5.0.9
  label: Ribbon
  search:
    terms:
      - cause
      - badge
      - pin
      - lapel
  styles:
    - solid
  unicode: f4d6
road:
  changes:
    - '1'
    - 5.0.0
  label: road
  search:
    terms:
      - street
  styles:
    - solid
  unicode: f018
robot:
  changes:
    - 5.0.13
  label: Robot
  search:
    terms: []
  styles:
    - solid
  unicode: f544
rocket:
  changes:
    - '3.1'
    - 5.0.0
  label: rocket
  search:
    terms:
      - app
  styles:
    - solid
  unicode: f135
rocketchat:
  changes:
    - 5.0.0
  label: Rocket.Chat
  search:
    terms: []
  sponsored:
    label: Rocket.Chat
    url: 'https://rocket.chat'
  styles:
    - brands
  unicode: f3e8
rockrms:
  changes:
    - 5.0.0
  label: Rockrms
  search:
    terms: []
  sponsored:
    label: Rock RMS
    url: 'http://rockrms.com'
  styles:
    - brands
  unicode: f3e9
rss:
  changes:
    - '2'
    - 5.0.0
  label: rss
  search:
    terms:
      - blog
  styles:
    - solid
  unicode: f09e
rss-square:
  changes:
    - '3.1'
    - 5.0.0
  label: RSS Square
  search:
    terms:
      - feed
      - blog
  styles:
    - solid
  unicode: f143
ruble-sign:
  changes:
    - '4'
    - 5.0.0
  label: Ruble Sign
  search:
    terms:
      - rub
      - rub
  styles:
    - solid
  unicode: f158
ruler:
  changes:
    - 5.0.13
  label: Ruler
  search:
    terms: []
  styles:
    - solid
  unicode: f545
ruler-combined:
  changes:
    - 5.0.13
  label: Ruler Combined
  search:
    terms: []
  styles:
    - solid
  unicode: f546
ruler-horizontal:
  changes:
    - 5.0.13
  label: Ruler Horizontal
  search:
    terms: []
  styles:
    - solid
  unicode: f547
ruler-vertical:
  changes:
    - 5.0.13
  label: Ruler Vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f548
rupee-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Indian Rupee Sign
  search:
    terms:
      - indian
      - inr
  styles:
    - solid
  unicode: f156
safari:
  changes:
    - '4.4'
    - 5.0.0
  label: Safari
  search:
    terms:
      - browser
  styles:
    - brands
  unicode: f267
sass:
  changes:
    - 5.0.0
  label: Sass
  search:
    terms: []
  styles:
    - brands
  unicode: f41e
save:
  changes:
    - '2'
    - 5.0.0
  label: Save
  search:
    terms:
      - floppy
      - floppy-o
  styles:
    - solid
    - regular
  unicode: f0c7
schlix:
  changes:
    - 5.0.0
  label: SCHLIX
  search:
    terms: []
  sponsored:
    label: SCHLIX
    url: 'http://schlix.com'
  styles:
    - brands
  unicode: f3ea
school:
  changes:
    - 5.0.13
  label: School
  search:
    terms: []
  sponsored:
    label: SHP
    url: 'http://shp.com'
  styles:
    - solid
  unicode: f549
screwdriver:
  changes:
    - 5.0.13
  label: Screwdriver
  search:
    terms:
      - tool
      - repair
      - fix
      - container
      - settings
      - admin
  styles:
    - solid
  unicode: f54a
scribd:
  changes:
    - '4.5'
    - 5.0.0
  label: Scribd
  search:
    terms: []
  styles:
    - brands
  unicode: f28a
search:
  changes:
    - '1'
    - 5.0.0
  label: Search
  search:
    terms:
      - magnify
      - zoom
      - enlarge
      - bigger
      - preview
  styles:
    - solid
  unicode: f002
search-minus:
  changes:
    - '1'
    - 5.0.0
    - 5.0.13
  label: Search Minus
  search:
    terms:
      - magnify
      - minify
      - zoom
      - smaller
      - zoom out
  styles:
    - solid
  unicode: f010
search-plus:
  changes:
    - '1'
    - 5.0.0
  label: Search Plus
  search:
    terms:
      - magnify
      - zoom
      - enlarge
      - bigger
      - zoom in
  styles:
    - solid
  unicode: f00e
searchengin:
  changes:
    - 5.0.0
  label: Searchengin
  search:
    terms: []
  sponsored:
    label: SearchEng.in
    url: 'http://searcheng.in'
  styles:
    - brands
  unicode: f3eb
seedling:
  changes:
    - 5.0.9
  label: Seedling
  search:
    terms: []
  styles:
    - solid
  unicode: f4d8
sellcast:
  changes:
    - 5.0.0
  label: Sellcast
  search:
    terms:
      - eercast
  styles:
    - brands
  unicode: f2da
sellsy:
  changes:
    - '4.3'
    - 5.0.0
  label: Sellsy
  search:
    terms: []
  styles:
    - brands
  unicode: f213
server:
  changes:
    - '4.3'
    - 5.0.0
  label: Server
  search:
    terms: []
  styles:
    - solid
  unicode: f233
servicestack:
  changes:
    - 5.0.0
  label: Servicestack
  search:
    terms: []
  sponsored:
    label: ServiceStack
    url: 'https://servicestack.net'
  styles:
    - brands
  unicode: f3ec
share:
  changes:
    - '1'
    - 5.0.0
  label: Share
  search:
    terms: []
  styles:
    - solid
  unicode: f064
share-alt:
  changes:
    - '4.1'
    - 5.0.0
  label: Alternate Share
  search:
    terms: []
  styles:
    - solid
  unicode: f1e0
share-alt-square:
  changes:
    - '4.1'
    - 5.0.0
  label: Alternate Share Square
  search:
    terms: []
  styles:
    - solid
  unicode: f1e1
share-square:
  changes:
    - '3.1'
    - 5.0.0
  label: Share Square
  search:
    terms:
      - social
      - send
  styles:
    - solid
    - regular
  unicode: f14d
shekel-sign:
  changes:
    - '4.2'
    - 5.0.0
  label: Shekel Sign
  search:
    terms:
      - ils
      - ils
  styles:
    - solid
  unicode: f20b
shield-alt:
  changes:
    - 5.0.0
  label: Alternate Shield
  search:
    terms:
      - shield
  styles:
    - solid
  unicode: f3ed
ship:
  changes:
    - '4.3'
    - 5.0.0
  label: Ship
  search:
    terms:
      - boat
      - sea
  styles:
    - solid
  unicode: f21a
shipping-fast:
  changes:
    - 5.0.7
  label: Shipping Fast
  search:
    terms: []
  styles:
    - solid
  unicode: f48b
shirtsinbulk:
  changes:
    - '4.3'
    - 5.0.0
  label: Shirts in Bulk
  search:
    terms: []
  styles:
    - brands
  unicode: f214
shoe-prints:
  changes:
    - 5.0.13
  label: Shoe Prints
  search:
    terms:
      - feet
      - footprints
      - steps
  sponsored:
    label: Smup
    url: 'https://www.atomsoftware.com.au'
  styles:
    - solid
  unicode: f54b
shopping-bag:
  changes:
    - '4.5'
    - 5.0.0
  label: Shopping Bag
  search:
    terms: []
  styles:
    - solid
  unicode: f290
shopping-basket:
  changes:
    - '4.5'
    - 5.0.0
  label: Shopping Basket
  search:
    terms: []
  styles:
    - solid
  unicode: f291
shopping-cart:
  changes:
    - '1'
    - 5.0.0
  label: shopping-cart
  search:
    terms:
      - checkout
      - buy
      - purchase
      - payment
  styles:
    - solid
  unicode: f07a
shower:
  changes:
    - '4.7'
    - 5.0.0
  label: Shower
  search:
    terms: []
  styles:
    - solid
  unicode: f2cc
sign:
  changes:
    - 5.0.9
  label: Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f4d9
sign-in-alt:
  changes:
    - 5.0.0
  label: Alternate Sign In
  search:
    terms:
      - enter
      - join
      - log in
      - login
      - sign up
      - sign in
      - signin
      - signup
      - arrow
      - sign-in
  styles:
    - solid
  unicode: f2f6
sign-language:
  changes:
    - '4.6'
    - 5.0.0
  label: Sign Language
  search:
    terms: []
  styles:
    - solid
  unicode: f2a7
sign-out-alt:
  changes:
    - 5.0.0
  label: Alternate Sign Out
  search:
    terms:
      - log out
      - logout
      - leave
      - exit
      - arrow
      - sign-out
  styles:
    - solid
  unicode: f2f5
signal:
  changes:
    - '1'
    - 5.0.0
  label: signal
  search:
    terms:
      - graph
      - bars
      - status
      - online
  styles:
    - solid
  unicode: f012
simplybuilt:
  changes:
    - '4.3'
    - 5.0.0
  label: SimplyBuilt
  search:
    terms: []
  styles:
    - brands
  unicode: f215
sistrix:
  changes:
    - 5.0.0
  label: SISTRIX
  search:
    terms: []
  sponsored:
    label: SISTRIX
    url: 'https://www.sistrix.de'
  styles:
    - brands
  unicode: f3ee
sitemap:
  changes:
    - '2'
    - 5.0.0
    - 5.0.13
  label: Sitemap
  search:
    terms:
      - directory
      - hierarchy
      - organization
      - information architecture
      - ia
  styles:
    - solid
  unicode: f0e8
sith:
  changes:
    - 5.0.12
  label: Sith
  search:
    terms: []
  styles:
    - brands
  unicode: f512
skull:
  changes:
    - 5.0.13
  label: Skull
  search:
    terms:
      - bones
      - skeleton
      - yorick
  styles:
    - solid
  unicode: f54c
skyatlas:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
  label: skyatlas
  search:
    terms: []
  styles:
    - brands
  unicode: f216
skype:
  changes:
    - '3.2'
    - 5.0.0
  label: Skype
  search:
    terms: []
  styles:
    - brands
  unicode: f17e
slack:
  changes:
    - '4.1'
    - 5.0.0
  label: Slack Logo
  search:
    terms:
      - hashtag
      - anchor
      - hash
  styles:
    - brands
  unicode: f198
slack-hash:
  changes:
    - 5.0.0
  label: Slack Hashtag
  search:
    terms:
      - hashtag
      - anchor
      - hash
  styles:
    - brands
  unicode: f3ef
sliders-h:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.11
  label: Horizontal Sliders
  search:
    terms:
      - settings
      - sliders
  styles:
    - solid
  unicode: f1de
slideshare:
  changes:
    - '4.2'
    - 5.0.0
  label: Slideshare
  search:
    terms: []
  styles:
    - brands
  unicode: f1e7
smile:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.9
  label: Smile
  search:
    terms:
      - face
      - emoticon
      - happy
      - approve
      - satisfied
      - rating
  styles:
    - solid
    - regular
  unicode: f118
smoking:
  changes:
    - 5.0.7
  label: Smoking
  search:
    terms:
      - smoking status
      - cigarette
      - nicotine
  styles:
    - solid
  unicode: f48d
smoking-ban:
  changes:
    - 5.0.13
  label: Smoking Ban
  search:
    terms:
      - no smoking
      - non-smoking
  styles:
    - solid
  unicode: f54d
snapchat:
  changes:
    - '4.6'
    - 5.0.0
  label: Snapchat
  search:
    terms: []
  styles:
    - brands
  unicode: f2ab
snapchat-ghost:
  changes:
    - '4.6'
    - 5.0.0
  label: Snapchat Ghost
  search:
    terms: []
  styles:
    - brands
  unicode: f2ac
snapchat-square:
  changes:
    - '4.6'
    - 5.0.0
  label: Snapchat Square
  search:
    terms: []
  styles:
    - brands
  unicode: f2ad
snowflake:
  changes:
    - '4.7'
    - 5.0.0
  label: Snowflake
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2dc
sort:
  changes:
    - '2'
    - 5.0.0
  label: Sort
  search:
    terms:
      - order
  styles:
    - solid
  unicode: f0dc
sort-alpha-down:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Alpha Down
  search:
    terms:
      - sort-alpha-asc
  styles:
    - solid
  unicode: f15d
sort-alpha-up:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Alpha Up
  search:
    terms:
      - sort-alpha-desc
  styles:
    - solid
  unicode: f15e
sort-amount-down:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Amount Down
  search:
    terms:
      - sort-amount-asc
  styles:
    - solid
  unicode: f160
sort-amount-up:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Amount Up
  search:
    terms:
      - sort-amount-desc
  styles:
    - solid
  unicode: f161
sort-down:
  changes:
    - '2'
    - 5.0.0
  label: Sort Down (Descending)
  search:
    terms:
      - arrow
      - descending
      - sort-desc
  styles:
    - solid
  unicode: f0dd
sort-numeric-down:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Numeric Down
  search:
    terms:
      - numbers
      - sort-numeric-asc
  styles:
    - solid
  unicode: f162
sort-numeric-up:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Numeric Up
  search:
    terms:
      - numbers
      - sort-numeric-desc
  styles:
    - solid
  unicode: f163
sort-up:
  changes:
    - '2'
    - 5.0.0
  label: Sort Up (Ascending)
  search:
    terms:
      - arrow
      - ascending
      - sort-asc
  styles:
    - solid
  unicode: f0de
soundcloud:
  changes:
    - '4.1'
    - 5.0.0
  label: SoundCloud
  search:
    terms: []
  styles:
    - brands
  unicode: f1be
space-shuttle:
  changes:
    - '4.1'
    - 5.0.0
  label: Space Shuttle
  search:
    terms:
      - nasa
      - astronaut
      - rocket
      - machine
      - transportation
  styles:
    - solid
  unicode: f197
speakap:
  changes:
    - 5.0.0
  label: Speakap
  search:
    terms: []
  sponsored:
    label: Speakap
    url: 'https://speakap.com'
  styles:
    - brands
  unicode: f3f3
spinner:
  changes:
    - '3'
    - 5.0.0
  label: Spinner
  search:
    terms:
      - loading
      - progress
  styles:
    - solid
  unicode: f110
spotify:
  changes:
    - '4.1'
    - 5.0.0
  label: Spotify
  search:
    terms: []
  styles:
    - brands
  unicode: f1bc
square:
  changes:
    - '2'
    - 5.0.0
  label: Square
  search:
    terms:
      - block
      - box
  styles:
    - solid
    - regular
  unicode: f0c8
square-full:
  changes:
    - 5.0.5
  label: Square Full
  search:
    terms: []
  styles:
    - solid
  unicode: f45c
stack-exchange:
  changes:
    - '4'
    - 5.0.0
    - 5.0.3
  label: Stack Exchange
  search:
    terms: []
  styles:
    - brands
  unicode: f18d
stack-overflow:
  changes:
    - '3.2'
    - 5.0.0
  label: Stack Overflow
  search:
    terms: []
  styles:
    - brands
  unicode: f16c
star:
  changes:
    - '1'
    - 5.0.0
  label: Star
  search:
    terms:
      - award
      - achievement
      - night
      - rating
      - score
      - favorite
  styles:
    - solid
    - regular
  unicode: f005
star-half:
  changes:
    - '1'
    - 5.0.0
  label: star-half
  search:
    terms:
      - award
      - achievement
      - rating
      - score
      - star-half-empty
      - star-half-full
  styles:
    - solid
    - regular
  unicode: f089
staylinked:
  changes:
    - 5.0.0
  label: StayLinked
  search:
    terms: []
  sponsored:
    label: StayLinked
    url: 'http://www.staylinked.com'
  styles:
    - brands
  unicode: f3f5
steam:
  changes:
    - '4.1'
    - 5.0.0
  label: Steam
  search:
    terms: []
  styles:
    - brands
  unicode: f1b6
steam-square:
  changes:
    - '4.1'
    - 5.0.0
  label: Steam Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1b7
steam-symbol:
  changes:
    - 5.0.0
  label: Steam Symbol
  search:
    terms: []
  styles:
    - brands
  unicode: f3f6
step-backward:
  changes:
    - '1'
    - 5.0.0
  label: step-backward
  search:
    terms:
      - rewind
      - previous
      - beginning
      - start
      - first
  styles:
    - solid
  unicode: f048
step-forward:
  changes:
    - '1'
    - 5.0.0
  label: step-forward
  search:
    terms:
      - next
      - end
      - last
  styles:
    - solid
  unicode: f051
stethoscope:
  changes:
    - '3'
    - 5.0.0
    - 5.0.7
  label: Stethoscope
  search:
    terms: []
  styles:
    - solid
  unicode: f0f1
sticker-mule:
  changes:
    - 5.0.0
  label: Sticker Mule
  search:
    terms: []
  sponsored:
    label: Sticker Mule
    url: 'https://stickermule.com'
  styles:
    - brands
  unicode: f3f7
sticky-note:
  changes:
    - '4.4'
    - 5.0.0
  label: Sticky Note
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f249
stop:
  changes:
    - '1'
    - 5.0.0
  label: stop
  search:
    terms:
      - block
      - box
      - square
  styles:
    - solid
  unicode: f04d
stop-circle:
  changes:
    - '4.5'
    - 5.0.0
  label: Stop Circle
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f28d
stopwatch:
  changes:
    - 5.0.0
  label: Stopwatch
  search:
    terms:
      - time
  styles:
    - solid
  unicode: f2f2
store:
  changes:
    - 5.0.13
  label: Store
  search:
    terms: []
  styles:
    - solid
  unicode: f54e
store-alt:
  changes:
    - 5.0.13
  label: Alternate Store
  search:
    terms: []
  styles:
    - solid
  unicode: f54f
strava:
  changes:
    - 5.0.0
    - 5.0.1
  label: Strava
  search:
    terms: []
  styles:
    - brands
  unicode: f428
stream:
  changes:
    - 5.0.13
  label: Stream
  search:
    terms: []
  sponsored:
    label: Administrator Technology
    url: 'https://administrator.de'
  styles:
    - solid
  unicode: f550
street-view:
  changes:
    - '4.3'
    - 5.0.0
  label: Street View
  search:
    terms:
      - map
  styles:
    - solid
  unicode: f21d
strikethrough:
  changes:
    - '2'
    - 5.0.0
  label: Strikethrough
  search:
    terms: []
  styles:
    - solid
  unicode: f0cc
stripe:
  changes:
    - 5.0.0
    - 5.0.3
  label: Stripe
  search:
    terms: []
  styles:
    - brands
  unicode: f429
stripe-s:
  changes:
    - 5.0.1
  label: Stripe S
  search:
    terms: []
  styles:
    - brands
  unicode: f42a
stroopwafel:
  changes:
    - 5.0.13
  label: Stroopwafel
  search:
    terms:
      - food
      - sweets
      - dessert
      - waffle
  sponsored:
    label: AppSignal
    url: 'https://appsignal.com'
  styles:
    - solid
  unicode: f551
studiovinari:
  changes:
    - 5.0.0
  label: Studio Vinari
  search:
    terms: []
  sponsored:
    label: Studio Vinari
    url: 'https://studiovinari.com'
  styles:
    - brands
  unicode: f3f8
stumbleupon:
  changes:
    - '4.1'
    - 5.0.0
  label: StumbleUpon Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a4
stumbleupon-circle:
  changes:
    - '4.1'
    - 5.0.0
  label: StumbleUpon Circle
  search:
    terms: []
  styles:
    - brands
  unicode: f1a3
subscript:
  changes:
    - '3.1'
    - 5.0.0
  label: subscript
  search:
    terms: []
  styles:
    - solid
  unicode: f12c
subway:
  changes:
    - '4.3'
    - 5.0.0
  label: Subway
  search:
    terms:
      - vehicle
      - train
      - railway
      - machine
      - transportation
  styles:
    - solid
  unicode: f239
suitcase:
  changes:
    - '3'
    - 5.0.0
    - 5.0.9
  label: Suitcase
  search:
    terms:
      - trip
      - luggage
      - travel
      - move
      - baggage
  styles:
    - solid
  unicode: f0f2
sun:
  changes:
    - '3.2'
    - 5.0.0
  label: Sun
  search:
    terms:
      - weather
      - contrast
      - lighter
      - brighten
      - day
  styles:
    - solid
    - regular
  unicode: f185
superpowers:
  changes:
    - '4.7'
    - 5.0.0
  label: Superpowers
  search:
    terms: []
  styles:
    - brands
  unicode: f2dd
superscript:
  changes:
    - '3.1'
    - 5.0.0
  label: superscript
  search:
    terms:
      - exponential
  styles:
    - solid
  unicode: f12b
supple:
  changes:
    - 5.0.0
  label: Supple
  search:
    terms: []
  styles:
    - brands
  unicode: f3f9
sync:
  changes:
    - '1'
    - 5.0.0
  label: Sync
  search:
    terms:
      - reload
      - refresh
      - exchange
      - swap
  styles:
    - solid
  unicode: f021
sync-alt:
  changes:
    - 5.0.0
  label: Alternate Sync
  search:
    terms:
      - reload
      - refresh
  styles:
    - solid
  unicode: f2f1
syringe:
  changes:
    - 5.0.7
  label: Syringe
  search:
    terms:
      - immunizations
      - needle
  styles:
    - solid
  unicode: f48e
table:
  changes:
    - '2'
    - 5.0.0
  label: table
  search:
    terms:
      - data
      - excel
      - spreadsheet
  styles:
    - solid
  unicode: f0ce
table-tennis:
  changes:
    - 5.0.5
  label: Table Tennis
  search:
    terms: []
  styles:
    - solid
  unicode: f45d
tablet:
  changes:
    - '3'
    - 5.0.0
  label: tablet
  search:
    terms:
      - device
      - screen
      - apple
      - ipad
      - kindle
  styles:
    - solid
  unicode: f10a
tablet-alt:
  changes:
    - 5.0.0
  label: Alternate Tablet
  search:
    terms:
      - device
      - screen
      - apple
      - ipad
      - kindle
  styles:
    - solid
  unicode: f3fa
tablets:
  changes:
    - 5.0.7
  label: Tablets
  search:
    terms:
      - medicine
      - drugs
  styles:
    - solid
  unicode: f490
tachometer-alt:
  changes:
    - 5.0.0
  label: Alternate Tachometer
  search:
    terms:
      - tachometer
      - dashboard
  styles:
    - solid
  unicode: f3fd
tag:
  changes:
    - '1'
    - 5.0.0
  label: tag
  search:
    terms:
      - label
  styles:
    - solid
  unicode: f02b
tags:
  changes:
    - '1'
    - 5.0.0
  label: tags
  search:
    terms:
      - labels
  styles:
    - solid
  unicode: f02c
tape:
  changes:
    - 5.0.9
  label: Tape
  search:
    terms: []
  styles:
    - solid
  unicode: f4db
tasks:
  changes:
    - '2'
    - 5.0.0
  label: Tasks
  search:
    terms:
      - progress
      - loading
      - downloading
      - downloads
      - settings
  styles:
    - solid
  unicode: f0ae
taxi:
  changes:
    - '4.1'
    - 5.0.0
  label: Taxi
  search:
    terms:
      - vehicle
      - machine
      - transportation
      - cab
      - cabbie
      - car
      - uber
      - lyft
      - car service
  styles:
    - solid
  unicode: f1ba
teamspeak:
  changes:
    - 5.0.11
  label: TeamSpeak
  search:
    terms: []
  styles:
    - brands
  unicode: f4f9
telegram:
  changes:
    - '4.7'
    - 5.0.0
  label: Telegram
  search:
    terms: []
  styles:
    - brands
  unicode: f2c6
telegram-plane:
  changes:
    - 5.0.0
  label: Telegram Plane
  search:
    terms: []
  styles:
    - brands
  unicode: f3fe
tencent-weibo:
  changes:
    - '4.1'
    - 5.0.0
  label: Tencent Weibo
  search:
    terms: []
  styles:
    - brands
  unicode: f1d5
terminal:
  changes:
    - '3.1'
    - 5.0.0
  label: Terminal
  search:
    terms:
      - command
      - prompt
      - code
      - console
  styles:
    - solid
  unicode: f120
text-height:
  changes:
    - '1'
    - 5.0.0
  label: text-height
  search:
    terms: []
  styles:
    - solid
  unicode: f034
text-width:
  changes:
    - '1'
    - 5.0.0
  label: text-width
  search:
    terms: []
  styles:
    - solid
  unicode: f035
th:
  changes:
    - '1'
    - 5.0.0
  label: th
  search:
    terms:
      - blocks
      - squares
      - boxes
      - grid
  styles:
    - solid
  unicode: f00a
th-large:
  changes:
    - '1'
    - 5.0.0
  label: th-large
  search:
    terms:
      - blocks
      - squares
      - boxes
      - grid
  styles:
    - solid
  unicode: f009
th-list:
  changes:
    - '1'
    - 5.0.0
  label: th-list
  search:
    terms:
      - ul
      - ol
      - checklist
      - finished
      - completed
      - done
      - todo
  styles:
    - solid
  unicode: f00b
themeisle:
  changes:
    - '4.6'
    - 5.0.0
  label: ThemeIsle
  search:
    terms: []
  styles:
    - brands
  unicode: f2b2
thermometer:
  changes:
    - 5.0.7
  label: Thermometer
  search:
    terms:
      - temperature
      - fever
  styles:
    - solid
  unicode: f491
thermometer-empty:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer Empty
  search:
    terms:
      - status
  styles:
    - solid
  unicode: f2cb
thermometer-full:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer Full
  search:
    terms:
      - status
  styles:
    - solid
  unicode: f2c7
thermometer-half:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer 1/2 Full
  search:
    terms:
      - status
  styles:
    - solid
  unicode: f2c9
thermometer-quarter:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer 1/4 Full
  search:
    terms:
      - status
  styles:
    - solid
  unicode: f2ca
thermometer-three-quarters:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer 3/4 Full
  search:
    terms:
      - status
  styles:
    - solid
  unicode: f2c8
thumbs-down:
  changes:
    - '3.2'
    - 5.0.0
  label: thumbs-down
  search:
    terms:
      - dislike
      - disapprove
      - disagree
      - hand
      - thumbs-o-down
  styles:
    - solid
    - regular
  unicode: f165
thumbs-up:
  changes:
    - '3.2'
    - 5.0.0
  label: thumbs-up
  search:
    terms:
      - like
      - favorite
      - approve
      - agree
      - hand
      - thumbs-o-up
      - success
      - 'yes'
      - ok
      - okay
      - you got it dude
  styles:
    - solid
    - regular
  unicode: f164
thumbtack:
  changes:
    - '1'
    - 5.0.0
  label: Thumbtack
  search:
    terms:
      - marker
      - pin
      - location
      - coordinates
      - thumb-tack
  styles:
    - solid
  unicode: f08d
ticket-alt:
  changes:
    - 5.0.0
  label: Alternate Ticket
  search:
    terms:
      - ticket
  styles:
    - solid
  unicode: f3ff
times:
  changes:
    - '1'
    - 5.0.0
    - 5.0.13
  label: Times
  search:
    terms:
      - close
      - exit
      - x
      - cross
      - error
      - problem
      - notification
      - notify
      - notice
      - wrong
      - incorrect
  styles:
    - solid
  unicode: f00d
times-circle:
  changes:
    - '1'
    - 5.0.0
  label: Times Circle
  search:
    terms:
      - close
      - exit
      - x
      - cross
      - problem
      - notification
      - notify
      - notice
      - wrong
      - incorrect
  styles:
    - solid
    - regular
  unicode: f057
tint:
  changes:
    - '1'
    - 5.0.0
  label: tint
  search:
    terms:
      - raindrop
      - waterdrop
      - drop
      - droplet
  styles:
    - solid
  unicode: f043
toggle-off:
  changes:
    - '4.2'
    - 5.0.0
  label: Toggle Off
  search:
    terms:
      - switch
  styles:
    - solid
  unicode: f204
toggle-on:
  changes:
    - '4.2'
    - 5.0.0
  label: Toggle On
  search:
    terms:
      - switch
  styles:
    - solid
  unicode: f205
toolbox:
  changes:
    - 5.0.13
  label: Toolbox
  search:
    terms:
      - tools
      - repair
      - fix
      - container
      - settings
      - admin
  sponsored:
    label: InSite Systems
    url: 'https://www.insitesystems.com'
  styles:
    - solid
  unicode: f552
trade-federation:
  changes:
    - 5.0.12
  label: Trade Federation
  search:
    terms: []
  styles:
    - brands
  unicode: f513
trademark:
  changes:
    - '4.4'
    - 5.0.0
  label: Trademark
  search:
    terms: []
  styles:
    - solid
  unicode: f25c
train:
  changes:
    - '4.3'
    - 5.0.0
  label: Train
  search:
    terms:
      - bullet
      - locomotive
      - railway
  styles:
    - solid
  unicode: f238
transgender:
  changes:
    - '4.3'
    - 5.0.0
  label: Transgender
  search:
    terms:
      - intersex
  styles:
    - solid
  unicode: f224
transgender-alt:
  changes:
    - '4.3'
    - 5.0.0
  label: Alternate Transgender
  search:
    terms: []
  styles:
    - solid
  unicode: f225
trash:
  changes:
    - '4.2'
    - 5.0.0
  label: Trash
  search:
    terms:
      - garbage
      - delete
      - remove
      - hide
  styles:
    - solid
  unicode: f1f8
trash-alt:
  changes:
    - 5.0.0
  label: Alternate Trash
  search:
    terms:
      - garbage
      - delete
      - remove
      - hide
      - trash
      - trash-o
  styles:
    - solid
    - regular
  unicode: f2ed
tree:
  changes:
    - '4.1'
    - 5.0.0
  label: Tree
  search:
    terms: []
  styles:
    - solid
  unicode: f1bb
trello:
  changes:
    - '3.2'
    - 5.0.0
  label: Trello
  search:
    terms: []
  styles:
    - brands
  unicode: f181
tripadvisor:
  changes:
    - '4.4'
    - 5.0.0
  label: TripAdvisor
  search:
    terms: []
  styles:
    - brands
  unicode: f262
trophy:
  changes:
    - '1'
    - 5.0.0
  label: trophy
  search:
    terms:
      - award
      - achievement
      - cup
      - winner
      - game
  styles:
    - solid
  unicode: f091
truck:
  changes:
    - '2'
    - 5.0.0
    - 5.0.7
  label: truck
  search:
    terms:
      - shipping
      - delivery
  styles:
    - solid
  unicode: f0d1
truck-loading:
  changes:
    - 5.0.9
  label: Truck Loading
  search:
    terms: []
  styles:
    - solid
  unicode: f4de
truck-moving:
  changes:
    - 5.0.9
  label: Truck Moving
  search:
    terms: []
  styles:
    - solid
  unicode: f4df
tshirt:
  changes:
    - 5.0.13
  label: T-Shirt
  search:
    terms: []
  styles:
    - solid
  unicode: f553
tty:
  changes:
    - '4.2'
    - 5.0.0
  label: TTY
  search:
    terms: []
  styles:
    - solid
  unicode: f1e4
tumblr:
  changes:
    - '3.2'
    - 5.0.0
  label: Tumblr
  search:
    terms: []
  styles:
    - brands
  unicode: f173
tumblr-square:
  changes:
    - '3.2'
    - 5.0.0
  label: Tumblr Square
  search:
    terms: []
  styles:
    - brands
  unicode: f174
tv:
  changes:
    - '4.4'
    - 5.0.0
  label: Television
  search:
    terms:
      - display
      - computer
      - monitor
      - television
  styles:
    - solid
  unicode: f26c
twitch:
  changes:
    - '4.2'
    - 5.0.0
  label: Twitch
  search:
    terms: []
  styles:
    - brands
  unicode: f1e8
twitter:
  changes:
    - '2'
    - 5.0.0
  label: Twitter
  search:
    terms:
      - tweet
      - social network
  styles:
    - brands
  unicode: f099
twitter-square:
  changes:
    - '1'
    - 5.0.0
  label: Twitter Square
  search:
    terms:
      - tweet
      - social network
  styles:
    - brands
  unicode: f081
typo3:
  changes:
    - 5.0.1
  label: Typo3
  search:
    terms: []
  sponsored:
    label: Typo3
    url: 'https://typo3.org'
  styles:
    - brands
  unicode: f42b
uber:
  changes:
    - 5.0.0
  label: Uber
  search:
    terms: []
  styles:
    - brands
  unicode: f402
uikit:
  changes:
    - 5.0.0
  label: UIkit
  search:
    terms: []
  styles:
    - brands
  unicode: f403
umbrella:
  changes:
    - '2'
    - 5.0.0
  label: Umbrella
  search:
    terms: []
  styles:
    - solid
  unicode: f0e9
underline:
  changes:
    - '2'
    - 5.0.0
  label: Underline
  search:
    terms: []
  styles:
    - solid
  unicode: f0cd
undo:
  changes:
    - '2'
    - 5.0.0
  label: Undo
  search:
    terms:
      - back
      - exchange
      - swap
      - return
      - control z
      - oops
  styles:
    - solid
  unicode: f0e2
undo-alt:
  changes:
    - 5.0.0
  label: Alternate Undo
  search:
    terms:
      - back
      - exchange
      - swap
      - return
      - control z
      - oops
  styles:
    - solid
  unicode: f2ea
uniregistry:
  changes:
    - 5.0.0
  label: Uniregistry
  search:
    terms: []
  sponsored:
    label: Uniregistry
    url: 'https://uniregistry.com'
  styles:
    - brands
  unicode: f404
universal-access:
  changes:
    - '4.6'
    - 5.0.0
  label: Universal Access
  search:
    terms: []
  styles:
    - solid
  unicode: f29a
university:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.3
  label: University
  search:
    terms:
      - bank
      - institution
  styles:
    - solid
  unicode: f19c
unlink:
  changes:
    - '3.1'
    - 5.0.0
  label: unlink
  search:
    terms:
      - remove
      - chain
      - chain-broken
  styles:
    - solid
  unicode: f127
unlock:
  changes:
    - '2'
    - 5.0.0
  label: unlock
  search:
    terms:
      - protect
      - admin
      - password
      - lock
  styles:
    - solid
  unicode: f09c
unlock-alt:
  changes:
    - '3.1'
    - 5.0.0
  label: Alternate Unlock
  search:
    terms:
      - protect
      - admin
      - password
      - lock
  styles:
    - solid
  unicode: f13e
untappd:
  changes:
    - 5.0.0
  label: Untappd
  search:
    terms: []
  styles:
    - brands
  unicode: f405
upload:
  changes:
    - '1'
    - 5.0.0
  label: Upload
  search:
    terms:
      - import
  styles:
    - solid
  unicode: f093
usb:
  changes:
    - '4.5'
    - 5.0.0
  label: USB
  search:
    terms: []
  styles:
    - brands
  unicode: f287
user:
  changes:
    - '1'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: User
  search:
    terms:
      - person
      - man
      - head
      - profile
      - account
  styles:
    - solid
    - regular
  unicode: f007
user-alt:
  changes:
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: Alternate User
  search:
    terms:
      - person
      - man
      - head
      - profile
      - account
  styles:
    - solid
  unicode: f406
user-alt-slash:
  changes:
    - 5.0.11
  label: Alternate User Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4fa
user-astronaut:
  changes:
    - 5.0.11
  label: User Astronaut
  search:
    terms: []
  styles:
    - solid
  unicode: f4fb
user-check:
  changes:
    - 5.0.11
  label: User Check
  search:
    terms: []
  styles:
    - solid
  unicode: f4fc
user-circle:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: User Circle
  search:
    terms:
      - person
      - man
      - head
      - profile
      - account
  styles:
    - solid
    - regular
  unicode: f2bd
user-clock:
  changes:
    - 5.0.11
  label: User Clock
  search:
    terms: []
  styles:
    - solid
  unicode: f4fd
user-cog:
  changes:
    - 5.0.11
  label: User Cog
  search:
    terms: []
  styles:
    - solid
  unicode: f4fe
user-edit:
  changes:
    - 5.0.11
  label: User Edit
  search:
    terms: []
  styles:
    - solid
  unicode: f4ff
user-friends:
  changes:
    - 5.0.11
  label: User Friends
  search:
    terms: []
  styles:
    - solid
  unicode: f500
user-graduate:
  changes:
    - 5.0.11
  label: User Graduate
  search:
    terms: []
  styles:
    - solid
  unicode: f501
user-lock:
  changes:
    - 5.0.11
  label: User Lock
  search:
    terms: []
  styles:
    - solid
  unicode: f502
user-md:
  changes:
    - '2'
    - 5.0.0
    - 5.0.3
    - 5.0.7
    - 5.0.11
  label: user-md
  search:
    terms:
      - doctor
      - profile
      - medical
      - nurse
      - job
      - occupation
  styles:
    - solid
  unicode: f0f0
user-minus:
  changes:
    - 5.0.11
  label: User Minus
  search:
    terms: []
  styles:
    - solid
  unicode: f503
user-ninja:
  changes:
    - 5.0.11
  label: User Ninja
  search:
    terms: []
  styles:
    - solid
  unicode: f504
user-plus:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: Add User
  search:
    terms:
      - sign up
      - signup
  styles:
    - solid
  unicode: f234
user-secret:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: User Secret
  search:
    terms:
      - whisper
      - spy
      - incognito
      - privacy
  styles:
    - solid
  unicode: f21b
user-shield:
  changes:
    - 5.0.11
  label: User Shield
  search:
    terms: []
  sponsored:
    label: mylogin.info
    url: 'https://www.mylogin.info'
  styles:
    - solid
  unicode: f505
user-slash:
  changes:
    - 5.0.11
  label: User Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f506
user-tag:
  changes:
    - 5.0.11
  label: User Tag
  search:
    terms: []
  styles:
    - solid
  unicode: f507
user-tie:
  changes:
    - 5.0.11
  label: User Tie
  search:
    terms: []
  styles:
    - solid
  unicode: f508
user-times:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: Remove User
  search:
    terms: []
  styles:
    - solid
  unicode: f235
users:
  changes:
    - '2'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: Users
  search:
    terms:
      - people
      - profiles
      - persons
  styles:
    - solid
  unicode: f0c0
users-cog:
  changes:
    - 5.0.11
  label: Users Cog
  search:
    terms: []
  styles:
    - solid
  unicode: f509
ussunnah:
  changes:
    - 5.0.0
  label: us-Sunnah Foundation
  search:
    terms: []
  sponsored:
    label: us-Sunnah Foundation
    url: 'https://www.ussunnah.org'
  styles:
    - brands
  unicode: f407
utensil-spoon:
  changes:
    - 5.0.0
  label: Utensil Spoon
  search:
    terms:
      - spoon
  styles:
    - solid
  unicode: f2e5
utensils:
  changes:
    - 5.0.0
  label: Utensils
  search:
    terms:
      - food
      - restaurant
      - spoon
      - knife
      - dinner
      - eat
      - cutlery
  styles:
    - solid
  unicode: f2e7
vaadin:
  changes:
    - 5.0.0
  label: Vaadin
  search:
    terms: []
  sponsored:
    label: Vaadin
    url: 'http://vaadin.com'
  styles:
    - brands
  unicode: f408
venus:
  changes:
    - '4.3'
    - 5.0.0
  label: Venus
  search:
    terms:
      - female
  styles:
    - solid
  unicode: f221
venus-double:
  changes:
    - '4.3'
    - 5.0.0
  label: Venus Double
  search:
    terms: []
  styles:
    - solid
  unicode: f226
venus-mars:
  changes:
    - '4.3'
    - 5.0.0
  label: Venus Mars
  search:
    terms: []
  styles:
    - solid
  unicode: f228
viacoin:
  changes:
    - '4.3'
    - 5.0.0
  label: Viacoin
  search:
    terms: []
  styles:
    - brands
  unicode: f237
viadeo:
  changes:
    - '4.6'
    - 5.0.0
  label: Viadeo
  search:
    terms: []
  styles:
    - brands
  unicode: f2a9
viadeo-square:
  changes:
    - '4.6'
    - 5.0.0
  label: Viadeo Square
  search:
    terms: []
  styles:
    - brands
  unicode: f2aa
vial:
  changes:
    - 5.0.7
  label: Vial
  search:
    terms:
      - test tube
  styles:
    - solid
  unicode: f492
vials:
  changes:
    - 5.0.7
  label: Vials
  search:
    terms:
      - lab results
      - test tubes
  styles:
    - solid
  unicode: f493
viber:
  changes:
    - 5.0.0
    - 5.0.3
  label: Viber
  search:
    terms: []
  styles:
    - brands
  unicode: f409
video:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: Video
  search:
    terms:
      - film
      - movie
      - record
      - camera
      - video-camera
  styles:
    - solid
  unicode: f03d
video-slash:
  changes:
    - 5.0.9
  label: Video Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4e2
vimeo:
  changes:
    - 5.0.0
  label: Vimeo
  search:
    terms: []
  styles:
    - brands
  unicode: f40a
vimeo-square:
  changes:
    - '4'
    - 5.0.0
  label: Vimeo Square
  search:
    terms: []
  styles:
    - brands
  unicode: f194
vimeo-v:
  changes:
    - '4.4'
    - 5.0.0
  label: Vimeo
  search:
    terms:
      - vimeo
  styles:
    - brands
  unicode: f27d
vine:
  changes:
    - '4.1'
    - 5.0.0
  label: Vine
  search:
    terms: []
  styles:
    - brands
  unicode: f1ca
vk:
  changes:
    - '3.2'
    - 5.0.0
  label: VK
  search:
    terms: []
  styles:
    - brands
  unicode: f189
vnv:
  changes:
    - 5.0.0
  label: VNV
  search:
    terms: []
  sponsored:
    label: VNV
    url: 'https://www.vnv.ch'
  styles:
    - brands
  unicode: f40b
volleyball-ball:
  changes:
    - 5.0.5
  label: Volleyball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f45f
volume-down:
  changes:
    - '1'
    - 5.0.0
  label: volume-down
  search:
    terms:
      - audio
      - lower
      - quieter
      - sound
      - music
      - speaker
  styles:
    - solid
  unicode: f027
volume-off:
  changes:
    - '1'
    - 5.0.0
  label: volume-off
  search:
    terms:
      - audio
      - mute
      - sound
      - music
  styles:
    - solid
  unicode: f026
volume-up:
  changes:
    - '1'
    - 5.0.0
  label: volume-up
  search:
    terms:
      - audio
      - higher
      - louder
      - sound
      - music
      - speaker
  styles:
    - solid
  unicode: f028
vuejs:
  changes:
    - 5.0.0
  label: Vue.js
  search:
    terms: []
  styles:
    - brands
  unicode: f41f
walking:
  changes:
    - 5.0.13
  label: Walking
  search:
    terms: []
  styles:
    - solid
  unicode: f554
wallet:
  changes:
    - 5.0.13
  label: Wallet
  search:
    terms: []
  styles:
    - solid
  unicode: f555
warehouse:
  changes:
    - 5.0.7
  label: Warehouse
  search:
    terms: []
  styles:
    - solid
  unicode: f494
weibo:
  changes:
    - '3.2'
    - 5.0.0
  label: Weibo
  search:
    terms: []
  styles:
    - brands
  unicode: f18a
weight:
  changes:
    - 5.0.7
  label: Weight
  search:
    terms:
      - scale
  styles:
    - solid
  unicode: f496
weixin:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.3
  label: Weixin (WeChat)
  search:
    terms: []
  styles:
    - brands
  unicode: f1d7
whatsapp:
  changes:
    - '4.3'
    - 5.0.0
  label: What's App
  search:
    terms: []
  styles:
    - brands
  unicode: f232
whatsapp-square:
  changes:
    - 5.0.0
  label: What's App Square
  search:
    terms: []
  styles:
    - brands
  unicode: f40c
wheelchair:
  changes:
    - '4'
    - 5.0.0
  label: Wheelchair
  search:
    terms:
      - handicap
      - person
  styles:
    - solid
  unicode: f193
whmcs:
  changes:
    - 5.0.0
  label: WHMCS
  search:
    terms: []
  sponsored:
    label: WHMCS
    url: 'https://www.whmcs.com'
  styles:
    - brands
  unicode: f40d
wifi:
  changes:
    - '4.2'
    - 5.0.0
  label: WiFi
  search:
    terms: []
  styles:
    - solid
  unicode: f1eb
wikipedia-w:
  changes:
    - '4.4'
    - 5.0.0
  label: Wikipedia W
  search:
    terms: []
  styles:
    - brands
  unicode: f266
window-close:
  changes:
    - '4.7'
    - 5.0.0
  label: Window Close
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f410
window-maximize:
  changes:
    - '4.7'
    - 5.0.0
  label: Window Maximize
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d0
window-minimize:
  changes:
    - '4.7'
    - 5.0.0
  label: Window Minimize
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d1
window-restore:
  changes:
    - '4.7'
    - 5.0.0
  label: Window Restore
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d2
windows:
  changes:
    - '3.2'
    - 5.0.0
  label: Windows
  search:
    terms:
      - microsoft
  styles:
    - brands
  unicode: f17a
wine-glass:
  changes:
    - 5.0.9
  label: Wine Glass
  search:
    terms: []
  styles:
    - solid
  unicode: f4e3
wolf-pack-battalion:
  changes:
    - 5.0.12
  label: Wolf Pack-battalion
  search:
    terms: []
  styles:
    - brands
  unicode: f514
won-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Won Sign
  search:
    terms:
      - krw
      - krw
  styles:
    - solid
  unicode: f159
wordpress:
  changes:
    - '4.1'
    - 5.0.0
  label: WordPress Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f19a
wordpress-simple:
  changes:
    - 5.0.0
  label: Wordpress Simple
  search:
    terms: []
  styles:
    - brands
  unicode: f411
wpbeginner:
  changes:
    - '4.6'
    - 5.0.0
  label: WPBeginner
  search:
    terms: []
  styles:
    - brands
  unicode: f297
wpexplorer:
  changes:
    - '4.7'
    - 5.0.0
  label: WPExplorer
  search:
    terms: []
  styles:
    - brands
  unicode: f2de
wpforms:
  changes:
    - '4.6'
    - 5.0.0
  label: WPForms
  search:
    terms: []
  styles:
    - brands
  unicode: f298
wrench:
  changes:
    - '2'
    - 5.0.0
    - 5.0.13
  label: Wrench
  search:
    terms:
      - settings
      - fix
      - update
      - spanner
      - tool
  styles:
    - solid
  unicode: f0ad
x-ray:
  changes:
    - 5.0.7
  label: X-Ray
  search:
    terms:
      - radiological images
      - radiology
  styles:
    - solid
  unicode: f497
xbox:
  changes:
    - 5.0.0
  label: Xbox
  search:
    terms: []
  styles:
    - brands
  unicode: f412
xing:
  changes:
    - '3.2'
    - 5.0.0
  label: Xing
  search:
    terms: []
  styles:
    - brands
  unicode: f168
xing-square:
  changes:
    - '3.2'
    - 5.0.0
  label: Xing Square
  search:
    terms: []
  styles:
    - brands
  unicode: f169
y-combinator:
  changes:
    - '4.4'
    - 5.0.0
  label: Y Combinator
  search:
    terms: []
  styles:
    - brands
  unicode: f23b
yahoo:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.3
  label: Yahoo Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f19e
yandex:
  changes:
    - 5.0.0
  label: Yandex
  search:
    terms: []
  styles:
    - brands
  unicode: f413
yandex-international:
  changes:
    - 5.0.0
  label: Yandex International
  search:
    terms: []
  styles:
    - brands
  unicode: f414
yelp:
  changes:
    - '4.2'
    - 5.0.0
  label: Yelp
  search:
    terms: []
  styles:
    - brands
  unicode: f1e9
yen-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Yen Sign
  search:
    terms:
      - jpy
      - jpy
  styles:
    - solid
  unicode: f157
yoast:
  changes:
    - '4.6'
    - 5.0.0
    - 5.0.3
  label: Yoast
  search:
    terms: []
  styles:
    - brands
  unicode: f2b1
youtube:
  changes:
    - '3.2'
    - 5.0.0
  label: YouTube
  search:
    terms:
      - video
      - film
      - youtube-play
      - youtube-square
  styles:
    - brands
  unicode: f167
youtube-square:
  changes:
    - 5.0.3
  label: YouTube Square
  search:
    terms: []
  styles:
    - brands
  unicode: f431
