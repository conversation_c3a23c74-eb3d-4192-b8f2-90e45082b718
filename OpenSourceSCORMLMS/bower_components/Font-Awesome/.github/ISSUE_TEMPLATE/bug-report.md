### Describe the problem

<!--- What happened? What are you seeing? How did you arrive here? -->

### What did you expect?

<!--- How would you like this to work instead? -->

### What version and implementation are you using?

Version: <!--- Give us the version number here -->
Browser and version: <!--- If applicable give us the browser specs -->

- [ ] SVG with JS
- [ ] Web Fonts with CSS
- [ ] SVG Sprites
- [ ] On the Desktop

### Reproducible test case

<!--- Insert a URL to your test case - use codepen.io, jsfiddle.net, jsbin.com, codesandbox.io, or whatever -->

<!--- Describe any details about the test case that we need to know like "whatever you do, don't click the red button" -->

### Bug report checklist

- [ ] I have filled out as much of the above information as I can
- [ ] I have included a test case because my odds go _way_ up that the team can fix this when I do
- [ ] I have [searched for existing issues](https://github.com/FortAwesome/Font-Awesome/issues) and to the best of my knowledge this is not a duplicate
