﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Home page";
}

<div class="jumbotron">
    <div class="container">
        <h1 class="display-3">Open Source SCORM LMS</h1>
        <p>This is an open source implementation of the SCORM 1.2 standard (with some support for SCORM 2004).</p>
        <p>It gives the ability to upload SCORM packages, play the courses in the built-in SCORM Player, and track results by student.</p>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <h2>Technology used:</h2>
        <ul>
            <li>ASP.Net CORE 2.1 with Razor Pages</li>
            <li>SQL Server</li>
            <li>Theming using <a href="https://go.microsoft.com/fwlink/?LinkID=398939">Bootstrap</a></li>
        </ul>
    </div>
    <div class="col-md-3">
        <h2>How to use this application:</h2>
        <ul>
            <li>Use as a base to build your own in-house LMS</li>
            <li>Use for educational purposes to learn how SCORM works</li>
            <li>Use to build your own SCORM Cloud</li>
            <li>By hosting your own site, you can avoid paying licensing fees to the big SCORM vendors</li>
            
        </ul>
    </div>
    <div class="col-md-3">
        <h2>Consulting</h2>
        <ul>
           <li>Need help customizing this Application? Outermost Software can help!</li>
        </ul>
    </div>
    <div class="col-md-3">
        <h2>Run &amp; Deploy</h2>
        <ul>
           
        </ul>
    </div>
</div>
