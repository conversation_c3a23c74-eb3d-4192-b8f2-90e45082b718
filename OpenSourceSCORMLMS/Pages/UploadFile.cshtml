﻿@page
@model OpenSourceSCORMLMS.Pages.UploadFileModel
@{
    ViewData["Title"] = "Upload";
}

<style>
    .dropDiv {
        width: 500px;
        height: 300px;
        border: solid;
        border-color: blue;
        border-width: 4;
    }
</style>
<script src="/js/dropzone.js"></script>
<h1>SCORM Package Upload</h1>
<div class="row">
    <div class="col-md-9">
        <div id="dropzone">
            <form method="post"  enctype="multipart/form-data" class="dropzone needsclick dz-clickable" id="uploader">
                <div class="dz-message needsclick dropDiv">
                    Drop SCORM packages (in zip files) here or click to upload.
                    <br>
                    After uploading, your package will launch in a separate window.
                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    Dropzone.autoDiscover = false;
    Dropzone.options.uploader = {
        paramName: "file",
        url: "/UploadFile",
        maxFilesize: 20,
        acceptedFiles: ".zip",
        dictInvalidFileType: "Please upload only SCORM packages in .zip format",
        init: function () {
            this.on("success", function (file, response) {
                $(".dz-success-mark svg").css("background", "green");
                $(".dz-error-mark").css("display", "none");
                // console.log(file);

                //   alert(response.message);
                window.location.href = response.url;
            });
            this.on("error", function (file) {
                $(".dz-error-mark svg").css("background", "red");
                $(".dz-success-mark").css("display", "none");
            });
        },

    };
    $('#uploader').dropzone();</script>



