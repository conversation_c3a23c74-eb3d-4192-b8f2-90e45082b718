@using Microsoft.AspNetCore.Identity

@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager

@if (SignInManager.IsSignedIn(User))
{
    <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Page("/Index", new { area = "" })" method="post" id="logoutForm" >
        <ul class="nav navbar-nav mr-auto">
            <li class="nav-item mr-auto">
                <a asp-area="Identity" asp-page="/Account/Manage/Index" title="Manage" class="nav-link mr-auto">Hello @UserManager.GetUserName(User)!</a>
            </li>
            <li class="nav-item mr-auto">
                <button type="submit" class="btn btn-link navbar-btn nav-link">Logout</button>
            </li>
        </ul>
    </form>
}
else
{
    <ul class="nav navbar-nav mr-auto">
        <li class="nav-item mr-auto"><a asp-area="Identity" asp-page="/Account/Register" class="nav-link mr-auto">Register</a></li>
        <li class="nav-item mr-auto"><a asp-area="Identity" asp-page="/Account/Login" class="nav-link mr-auto">Login</a></li>
    </ul>
}