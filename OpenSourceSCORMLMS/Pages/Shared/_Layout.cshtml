﻿@using Microsoft.AspNetCore.Identity

@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager


<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - OpenSourceSCORMLMS</title>

    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">

    <link rel="stylesheet" href="~/css/site.css" />
  
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-light navbar-expand-lg py-md-2 bg-light">
            <a class="navbar-brand" asp-page="/Index">OpenSource SCORM LMS</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="navbar-collapse collapse" id="navbarNav">
                <ul class="navbar-nav">

                    @if (SignInManager.IsSignedIn(User))
                    {
                        <li class="nav-item py-md-2"><a asp-page="/Index" class="nav-link">Home</a></li>
                        <li class="nav-item py-md-2"><a asp-page="/UploadFile" class="nav-link">Upload SCORM Package</a></li>
                        <li class="nav-item py-md-2"><a asp-page="MyCoursesForStudy" class="nav-link">My Courses for Study</a></li>
                        <li class="nav-item py-md-2"><a asp-page="/PackageListing" class="nav-link">All Courses</a></li>

                    }
                    else
                    {
                        <li class="nav-item py-md-2"><a asp-page="/Index" class="nav-link">Home</a></li>
                        <li class="nav-item py-md-2"><a asp-page="/About" class="nav-link">About</a></li>
                        <li class="nav-item py-md-2"><a asp-page="/Contact" class="nav-link">Contact</a></li>
                    }
                </ul>

            </div>
            <div class="navbar-collapse collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <partial name="_LoginPartial" />
                    </ul>
                </div>
        </nav>

        @* <partial name="_CookieConsentPartial" />*@
    </div>
    <div class="container body-content">
        @RenderBody()
        <hr />
        <footer>
            <p>&copy; 2018 - OpenSourceSCORMLMS</p>
        </footer>
    </div>



    @RenderSection("Scripts", required: false)
</body>
</html>
