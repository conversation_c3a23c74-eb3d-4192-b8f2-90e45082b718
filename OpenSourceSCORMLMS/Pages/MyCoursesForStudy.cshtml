﻿@page
@model OpenSourceSCORMLMS.Pages.MyCoursesForStudyModel
@{
    ViewData["Title"] = "MyCourses For Study";
}

<h2>My Courses For Study</h2>

<table id="gridPackages" class="table table-striped">
    <thead>
        <tr>
            <th>Title</th>
            <th>ModuleID</th>
            <th>Date Added </th>
            <th>Date Completed</th>
            <th>Date Passed</th>
            <th>Score</th>
            <th>Launch</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.listModules)
        {
        <tr>
            <td>@item.SCORM_Course.title_from_manifest

            <td>
                @item.id;
            </td>
            <td>
                @item.dtDateAdded
            </td>
            <td>
                @item.dtDateCompleted
            </td>
            <td>
                @item.dtDatePassed
            </td>
            <td>
                @item.dScore
            </td>
            <td>
                <a href="SCORMPlayer?id=@item.SCORM_Course.id" target="_blank">Launch this Course</a>
            </td>
        </tr>
        }
    </tbody>
</table>
