{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=scorm_lms.db"
  },
  "Logging": {
    "File": {
      "BasePath": "Logs",
      "EnsureBasePath": true,
      "FileEncoding": "utf-8",
      "FileNameMappings": {
        "Default": "default.log"
      },
      "DateFormat": "yyyyMMdd",
      "CounterFormat": "000",
      "MaxFileSize": 10485760,
      "LogLevel": {
        "MyApp": "Information",
        "Default": "Warning"
      },
      "IncludeScopes": true,
      "MaxQueueSize": 100
    }
  },
  // global filter settings
  "LogLevel": {
    "Default": "Information"
  },
  "AppSettings": {
    "UploadFolder": "SCORMPackages/",
    "CourseFolder": "SCORMCourses/",
    "SiteUrl": "https://locahost:5001"
   
  },
  "AllowedHosts": "*"
}
